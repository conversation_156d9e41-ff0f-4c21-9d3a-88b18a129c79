###  1.门店登录  请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json

{
  "username": "15926351000",
  "password": "Abc123456"
}

> {%
  client.global.set("tenantId", response.body.data.tenantId == null ? response.body.data.tenantList[0].id : response.body.data.tenantId);
  client.global.set("token", response.body.data.accessToken);
  console.log(response.body.data.accessToken);
  client.global.set("loginUserId", response.body.data.userId);
  client.global.set("date", new Date());
%}


### 选择门店
POST {{baseAdminSystemUrl}}/system/auth/login-confirm-with-tenant-id
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "tenantId": "{{tenantId}}",
  "username": "15926351000",
  "userId": "{{loginUserId}}",
  "token": "{{token}}"
}

> {%
  client.global.set("tenantId", response.body.data.tenantId == null ? response.body.data.tenantList[0].id : response.body.data.tenantId);
  client.global.set("token", response.body.data.accessToken);
  console.log(response.body.data.accessToken);
  client.global.set("loginUserId", response.body.data.userId);
  client.global.set("date", new Date());
%}

### 2.查系统诊断 - 模拟手选
GET {{baseAppKernelUrl}}/kernel/hospital/inquiry-diagnosis/pages?diagnosisType=1
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data.list.length > 0 && !client.global.get("diagnosis")) {
    let diagnosis = [{
      "diagnosisCode": response.body.data.list[0].diagnosisCode,
      "diagnosisName": response.body.data.list[0].diagnosisName
    }]
    client.global.set("diagnosis", JSON.stringify(diagnosis));
  }
%}

### 6.去问诊下单
POST  {{baseAppKernelUrl}}/kernel/patient/remote-audit/create-inquiry
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "clientChannelType": 0,
  "clientOsType": "android",
  "inquiryBizType": 2,
  "bizChannelType": 0,
  "remoteAuditBaseReqVO": {
    "patient": {
      "patientPref": "",
      "patientName": "张远志测试c",
      "patientMobile": "15171199701",
      "patientIdCard": "******************",
      "patientAge": "23",
      "patientSex": 1
    },
    "diagnosis": {{diagnosis}},
    "thirdPrescriptionNo": "CF0000000002",
    "hospitalName": "武汉普仁医院",
    "outPrescriptionTime": 1745488536000,
    "doctorName": "王普仁",
    "medicineType": 0,
    "offlinePrescriptions": ["https://files.test.ybm100.com/INVT/Lzinq/20250422/37cff74b11e489a61059541f11f819037e1e400b51f1edc8fd317f32e652be4e.jpg"],
    "prescriptionImg": "https://files.test.ybm100.com/INVT/Lzinq/20250422/37cff74b11e489a61059541f11f819037e1e400b51f1edc8fd317f32e652be4e.jpg"
  }
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  client.global.set("inquiryPref", response.body.data.inquiryPref);
%}


### 4.取消问诊
PUT {{baseAppKernelUrl}}/kernel/patient/inquiry/inquiry-cancel?inquiryPref={{inquiryPref}}}
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}