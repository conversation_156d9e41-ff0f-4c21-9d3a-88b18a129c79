### 请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{
  "username": "15926351111",
  "password": "Abc123456"
}

> {%
  client.global.set("token", response.body.data.accessToken);
%}


### 2.查商品信息 - 西药
GET {{baseAppKernelUrl}}/kernel/signature/inquiry-signature-ca-auth/get
Content-Type: application/json
Authorization: Bearer {{token}}


### IM
PUT {{baseAdminKernelUrl}}/kernel/hospital/saas-prescription-external/retry-upload?id=1943955846977372186
Authorization: Bearer {{token}}

