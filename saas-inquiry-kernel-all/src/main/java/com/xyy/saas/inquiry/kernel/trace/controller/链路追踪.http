###  1.门店登录  请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "Admin123"
}

> {%
  client.global.set("tenantId", response.body.data.tenantId == null ? response.body.data.tenantList[0].id : response.body.data.tenantId);
  client.global.set("token", response.body.data.accessToken);
  console.log(response.body.data.accessToken);
  client.global.set("loginUserId", response.body.data.userId);
%}

### 2.列表查询
GET {{baseAdminKernelUrl}}/kernel/trace/page?nodeCode=MATCH_DOCTOR&pageNo=1&pageSize=10&orderBy=startTime&orderDirection=desc
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}


