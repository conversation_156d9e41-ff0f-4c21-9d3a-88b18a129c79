package com.xyy.saas.inquiry.kernel.inquiryRecord;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.INQUIRY_RECORD_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.inquiry.kernel.BaseIntegrationTest;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordRespVO;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordSaveReqVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.DrugstoreInquiryReqVO;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDO;
import com.xyy.saas.inquiry.patient.dal.mysql.inquiry.InquiryRecordMapper;
import com.xyy.saas.inquiry.patient.service.inquiry.impl.InquiryServiceImpl;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;


/**
 * {@link InquiryServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(InquiryServiceImpl.class)
public class InquiryRecordServiceImplTest extends BaseIntegrationTest {

    @Resource
    private InquiryServiceImpl inquiryService;

    @Resource
    private InquiryRecordMapper inquiryRecordMapper;


    @BeforeEach
    public void before() {
        RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
        requestAttributes.setAttribute("login_user_id", 1856621117343469570L, RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.setRequestAttributes(requestAttributes);
//        TenantContextHolder.setTenantId();
    }

    @Test
    // 去问诊
    public void testGotoInquiry() {
        String json = "{\"clientChannelType\":0,\"clientOsType\":\"android\",\"inquiryWayType\":1,\"bizChannelType\":0,\"baseInquiryReqVO\":{\"patient\":{\"patientName\":\"张美丽\",\"patientMobile\":\"13235528029\",\"patientIdCard\":\"******************\",\"patientAge\":\"26\",\"patientSex\":2},\"mainSuit\":[\"头痛\",\"咽痛\"],\"allergic\":[\"头孢\",\"青霉素\"],\"diagnosis\":[{\"diagnosisCode\":\"001\",\"diagnosisName\":\"感冒\"},{\"diagnosisCode\":\"002\",\"diagnosisName\":\"上呼吸道感染\"}],\"slowDisease\":0,\"liverKidneyValue\":3,\"gestationLactationValue\":3,\"followUp\":0,\"medicineType\":0,\"offlinePrescriptions\":[],\"inquiryProductInfo\":{\"inquiryProductInfos\":[{\"commonName\":\"阿莫西林胶囊\",\"attributeSpecification\":\"0.125g*100s\",\"quantity\":1,\"isClick\":true,\"directions\":\"口服\",\"singleDose\":\"4\",\"singleUnit\":\"粒\",\"useFrequency\":\"3次/天\",\"pref\":\"847778\",\"productName\":\"阿莫西林胶囊\",\"type\":1,\"prescriptionYn\":\"1\",\"specifications\":\"0.125g*100s\",\"manufacturer\":\"中山市力恩普制药有限公司\",\"unitName\":\"盒\",\"useFrequencyValue\":\"THREETIMESONEDAY\",\"singleDoseValue\":\"4\",\"singleUnitValue\":\"粒\",\"pharmacyPref\":\"国药准字H44023590\"}]}}}";
        // 准备参数
        DrugstoreInquiryReqVO createReqVO = JSONObject.parseObject(json, DrugstoreInquiryReqVO.class);

        // 调用
        // String pref = inquiryService.createInquiryRecord(createReqVO);
        // // 断言
        // assertNotNull(pref);
        // 校验记录的属性是否正确
        // InquiryRecordDO inquiryRecord = inquiryRecordMapper.selectOne(InquiryRecordDO::getPref, pref);
        // assertEquals(inquiryRecord.getPatientName(), createReqVO.getBaseInquiryReqVO().getPatient().getPatientName());
    }


    @Test
    public void testUpdateInquiryRecord_success() {
        // mock 数据
        InquiryRecordDO dbInquiryRecord = randomPojo(InquiryRecordDO.class);
        inquiryRecordMapper.insert(dbInquiryRecord);// @Sql: 先插入出一条存在的数据
        // 准备参数
        InquiryRecordSaveReqVO updateReqVO = randomPojo(InquiryRecordSaveReqVO.class, o -> {
            o.setId(dbInquiryRecord.getId()); // 设置更新的 ID
        });

        // 调用
        inquiryService.updateInquiryRecord(updateReqVO);
        // 校验是否更新正确
        InquiryRecordDO inquiryRecord = inquiryRecordMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, inquiryRecord);
    }

    @Test
    public void testUpdateInquiryRecord_notExists() {
        // 准备参数
        InquiryRecordSaveReqVO updateReqVO = randomPojo(InquiryRecordSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> inquiryService.updateInquiryRecord(updateReqVO), INQUIRY_RECORD_NOT_EXISTS);
    }

    @Test
    public void testDeleteInquiryRecord_success() {
        // mock 数据
        InquiryRecordDO dbInquiryRecord = randomPojo(InquiryRecordDO.class);
        inquiryRecordMapper.insert(dbInquiryRecord);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbInquiryRecord.getId();

        // 调用
        inquiryService.deleteInquiryRecord(id);
        // 校验数据不存在了
        assertNull(inquiryRecordMapper.selectById(id));
    }

    @Test
    public void testDeleteInquiryRecord_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> inquiryService.deleteInquiryRecord(id), INQUIRY_RECORD_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetInquiryRecordPage() {
        // mock 数据
        InquiryRecordDO dbInquiryRecord = randomPojo(InquiryRecordDO.class, o -> { // 等会查询到
            o.setPref(null);
            o.setPatientName(null);
            o.setPatientAge(null);
            o.setPatientSex(null);
            o.setPatientMobile(null);
            o.setHospitalName(null);
            o.setDeptName(null);
            o.setPreTempId(null);
            o.setInquiryStatus(null);
            o.setCancelReason(null);
            o.setInquiryWayType(null);
            o.setInquiryBizType(null);
            o.setClientChannelType(null);
            o.setClientOsType(null);
            o.setBizChannelType(null);
            o.setMedicineType(null);
            o.setAutoInquiry(null);
            o.setUnableAutoReason(null);
            o.setImPlatform(null);
            o.setOrderNo(null);
            o.setStartTime(null);
            o.setEndTime(null);
            o.setMp4Url(null);
            o.setImPdf(null);
            o.setStreamId(null);
            o.setTranscodingId(null);
            o.setCreateTime(null);
        });
        inquiryRecordMapper.insert(dbInquiryRecord);
        // 测试 pref 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setPref(null)));
        // 测试 patientName 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setPatientName(null)));
        // 测试 patientAge 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setPatientAge(null)));
        // 测试 patientSex 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setPatientSex(null)));
        // 测试 patientMobile 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setPatientMobile(null)));
        // 测试 hospitalName 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setHospitalName(null)));
        // 测试 deptName 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setDeptName(null)));
        // 测试 preTempId 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setPreTempId(null)));
        // 测试 inquiryStatus 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setInquiryStatus(null)));
        // 测试 cancelReason 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setCancelReason(null)));
        // 测试 inquiryWayType 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setInquiryWayType(null)));
        // 测试 inquiryBizType 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setInquiryBizType(null)));
        // 测试 clinetChannelType 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setClientChannelType(null)));
        // 测试 clinetOsType 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setClientOsType(null)));
        // 测试 bizChannelType 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setBizChannelType(null)));
        // 测试 medicineType 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setMedicineType(null)));
        // 测试 autoInquiry 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setAutoInquiry(null)));
        // 测试 unbleAutoReason 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setUnableAutoReason(null)));
        // 测试 imPlatform 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setImPlatform(null)));
        // 测试 orderNo 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setOrderNo(null)));
        // 测试 startTime 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setStartTime(null)));
        // 测试 endTime 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setEndTime(null)));
        // 测试 mp4Url 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setMp4Url(null)));
        // 测试 imPdf 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setImPdf(null)));
        // 测试 streamId 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setStreamId(null)));
        // 测试 transcodingId 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setTranscodingId(null)));
        // 测试 createTime 不匹配
        inquiryRecordMapper.insert(cloneIgnoreId(dbInquiryRecord, o -> o.setCreateTime(null)));
        // 准备参数
        InquiryRecordPageReqVO reqVO = new InquiryRecordPageReqVO();
        reqVO.setPref(null);
        reqVO.setInquiryStatus(null);
        reqVO.setInquiryWayType(null);
        reqVO.setInquiryBizType(null);
        reqVO.setBizChannelType(null);
        reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

        // 调用
        PageResult<InquiryRecordRespVO> pageResult = inquiryService.getInquiryRecordPage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbInquiryRecord, pageResult.getList().get(0));
    }

}