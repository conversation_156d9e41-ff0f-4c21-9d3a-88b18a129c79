package cn.iocoder.yudao.module.member.service.auth;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.enums.TerminalEnum;
import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.common.util.monitor.TracerUtils;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.member.controller.app.auth.vo.AppAuthLoginRespVO;
import cn.iocoder.yudao.module.member.controller.app.auth.vo.AppAuthWeixinMiniAppLoginReqVO;
import cn.iocoder.yudao.module.member.controller.admin.auth.vo.ZhlBindAccountReqVO;
import cn.iocoder.yudao.module.member.controller.admin.auth.vo.ZhlBindAccountRespVO;
import cn.iocoder.yudao.module.member.controller.admin.auth.vo.ZhlBindTenantReqVO;
import cn.iocoder.yudao.module.member.controller.admin.auth.vo.ZhlBindTenantRespVO;
import cn.iocoder.yudao.module.member.controller.admin.auth.vo.ZhlTicketLoginReqVO;
import cn.iocoder.yudao.module.member.convert.auth.AuthConvert;
import cn.iocoder.yudao.module.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.member.service.user.MemberUserService;
import cn.iocoder.yudao.module.system.api.logger.LoginLogApi;
import cn.iocoder.yudao.module.system.api.logger.dto.LoginLogCreateReqDTO;
import cn.iocoder.yudao.module.system.api.oauth2.OAuth2TokenApi;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenCreateReqDTO;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenRespDTO;
import cn.iocoder.yudao.module.system.api.social.SocialClientApi;
import cn.iocoder.yudao.module.system.api.social.SocialUserApi;
import cn.iocoder.yudao.module.system.api.social.dto.SocialUserBindReqDTO;
import cn.iocoder.yudao.module.system.api.social.dto.SocialUserReqDTO;
import cn.iocoder.yudao.module.system.api.social.dto.SocialUserRespDTO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantSimpleRespVO;
import cn.iocoder.yudao.module.system.dal.dataobject.social.SocialUserDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.yudao.module.system.enums.logger.LoginLogTypeEnum;
import cn.iocoder.yudao.module.system.enums.logger.LoginResultEnum;
import cn.iocoder.yudao.module.system.enums.oauth2.OAuth2ClientConstants;
import cn.iocoder.yudao.module.system.enums.social.SocialTypeEnum;
import cn.iocoder.yudao.module.system.service.auth.AdminAuthService;
import cn.iocoder.yudao.module.system.service.permission.PermissionService;
import cn.iocoder.yudao.module.system.service.social.SocialUserService;
import cn.iocoder.yudao.module.system.service.tenant.TenantService;
import cn.iocoder.yudao.module.system.service.user.AdminUserService;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.servlet.ServletUtils.getClientIP;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.AUTH_LOGIN_USER_DISABLED;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_NOT_EXISTS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.ZHL_TICKET_LOGIN_ERROR;

/**
 * 会员的认证 Service 接口
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MemberAuthServiceImpl implements MemberAuthService{

    @Resource
    private SocialClientApi socialClientApi;

    @Resource
    private MemberUserService userService;

    @Resource
    private SocialUserApi socialUserApi;

    @Resource
    private LoginLogApi loginLogApi;

    @Resource
    private OAuth2TokenApi oauth2TokenApi;

    @Resource
    private AdminAuthService adminAuthService;

    @Resource
    private AdminUserService adminUserService;

    @Resource
    private TenantService tenantService;

    @Resource
    private SocialUserService socialUserService;

    @Resource
    private PermissionService permissionService;

    /**
     * 微信小程序的一键登录
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    @Override
    public AppAuthLoginRespVO weixinMiniAppLogin(AppAuthWeixinMiniAppLoginReqVO reqVO) {
        // 获取社交用户信息
        SocialUserRespDTO socialUserRespDTO = socialUserApi.getSocialUserByCode(getUserType().getValue(),SocialTypeEnum.WECHAT_MINI_APP.getType(), reqVO.getLoginCode(), reqVO.getState());
        MemberUserDO user = null;
        // 是否绑定memberUser
        if(ObjectUtil.isEmpty(socialUserRespDTO.getUserId())){
            // 新增用户
            user = userService.createUser(socialUserRespDTO.getNickname(),socialUserRespDTO.getAvatar(),getClientIP(),TerminalEnum.WECHAT_MINI_PROGRAM.getTerminal());
        }else{
            // 获取用户
            user = userService.getUser(socialUserRespDTO.getUserId());
        }
        // 绑定社交用户
        String openid = socialUserApi.bindSocialUser(new SocialUserBindReqDTO(user.getId(), getUserType().getValue(),
            SocialTypeEnum.WECHAT_MINI_APP.getType(), reqVO.getLoginCode(), reqVO.getState()));

        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user, user.getMobile(), LoginLogTypeEnum.LOGIN_SOCIAL, openid);
    }


    private AppAuthLoginRespVO createTokenAfterLoginSuccess(MemberUserDO user, String mobile,
        LoginLogTypeEnum logType, String openid) {
        // 插入登陆日志
        createLoginLog(user.getId(), mobile, logType, LoginResultEnum.SUCCESS);
        // 创建 Token 令牌
        OAuth2AccessTokenRespDTO accessTokenRespDTO = oauth2TokenApi.createAccessToken(new OAuth2AccessTokenCreateReqDTO()
            .setUserId(user.getId()).setUserType(getUserType().getValue())
            .setClientId(OAuth2ClientConstants.CLIENT_ID_DEFAULT));
        // 构建返回结果
        return AuthConvert.INSTANCE.convert(accessTokenRespDTO, openid);
    }

    private void createLoginLog(Long userId, String mobile, LoginLogTypeEnum logType, LoginResultEnum loginResult) {
        // 插入登录日志
        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        reqDTO.setLogType(logType.getType());
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        reqDTO.setUserType(getUserType().getValue());
        reqDTO.setUsername(mobile);
        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        reqDTO.setUserIp(getClientIP());
        reqDTO.setResult(loginResult.getResult());
        loginLogApi.createLoginLog(reqDTO);
        // 更新最后登录时间
        if (userId != null && Objects.equals(LoginResultEnum.SUCCESS.getResult(), loginResult.getResult())) {
            userService.updateUserLogin(userId, getClientIP());
        }
    }

    private UserTypeEnum getUserType() {
        return UserTypeEnum.MEMBER;
    }

    @Override
    public ZhlBindTenantRespVO zhlBindTenant(ZhlBindTenantReqVO reqVO) {

        AdminUserDO adminUserDO = null;
        if (StringUtils.isNotBlank(reqVO.getPassword())) {
            // 账号密码登录
            adminUserDO = adminAuthService.authenticate(reqVO.getUsername(), reqVO.getPassword());
        } else {
            // 根据手机号获得用户信息
            adminUserDO = adminUserService.getUserByMobileSystem(reqVO.getUsername());
        }
        if (adminUserDO == null) {
            throw exception(USER_NOT_EXISTS);
        }
        // 校验是否禁用
        if (CommonStatusEnum.isDisable(adminUserDO.getStatus())) {
            throw exception(AUTH_LOGIN_USER_DISABLED);
        }

        List<TenantDO> tenantDOList = tenantService.getTenantByAdminUserId(adminUserDO.getId());

        if (CollectionUtils.isEmpty(tenantDOList)) {
            return null;
        }

        List<TenantSimpleRespVO> tenantSimpleRespVOList = tenantDOList.stream()
            .filter(item -> CommonStatusEnum.ENABLE.getStatus().equals(item.getStatus()))
            .map(item -> {
                TenantSimpleRespVO tenantSimpleRespVO = new TenantSimpleRespVO();
                tenantSimpleRespVO.setId(item.getId());
                tenantSimpleRespVO.setType(item.getType());
                tenantSimpleRespVO.setPref(item.getPref());
                tenantSimpleRespVO.setName(item.getName());
                tenantSimpleRespVO.setAddress(item.getAddress());
                return tenantSimpleRespVO;
        }).toList();

        ZhlBindTenantRespVO zHlBindTenantRespVO = new ZhlBindTenantRespVO();
        if (tenantSimpleRespVOList.size() == 1) {
            zHlBindTenantRespVO.setTenantId(tenantSimpleRespVOList.getFirst().getId());
        } else {
            zHlBindTenantRespVO.setTenantList(tenantSimpleRespVOList);
        }

        return zHlBindTenantRespVO;
    }

    @Override
    public ZhlBindAccountRespVO zhlBindAccount(ZhlBindAccountReqVO reqVO) {

        String guid = UUID.randomUUID().toString().replace("-", "");
        SocialUserReqDTO socialUserReqDTO = new SocialUserReqDTO();
        socialUserReqDTO.setType(SocialTypeEnum.ZHL.getType());
        socialUserReqDTO.setOpenid(guid);
        socialUserReqDTO.setToken("");
        socialUserReqDTO.setRawTokenInfo("");
        socialUserReqDTO.setNickname("");
        socialUserReqDTO.setAvatar("");
        socialUserReqDTO.setRawUserInfo("");
        socialUserReqDTO.setCode(guid);
        socialUserReqDTO.setState("0");
        // 创建社交用户信息
        SocialUserDO socialUserDO = socialUserService.createSocialUser(socialUserReqDTO);

        // 新增用户
        MemberUserDO user = userService.createUser(socialUserDO.getNickname(), socialUserDO.getAvatar(), getClientIP(), TerminalEnum.H5.getTerminal());
        // 用户绑定智慧脸员工角色
        TenantUtils.execute(reqVO.getTenantId() , () -> permissionService.assignUserRoleWithSystemRoleCodes(user.getId(), Lists.newArrayList(RoleCodeEnum.ZHL_EMPLOYEE.getCode())));

        // 绑定社交用户
        socialUserService.bindSocialUser(socialUserDO.getId(), SocialTypeEnum.ZHL.getType(), user.getId(), getUserType().getValue());

        return ZhlBindAccountRespVO.builder().openid(socialUserDO.getOpenid()).build();
    }

    @Override
    public AppAuthLoginRespVO zhlTicketLogin(ZhlTicketLoginReqVO reqVO) {

        SocialUserRespDTO socialUserRespDTO = socialUserApi.getSocialUserByOpenid(reqVO.getOpenid(), SocialTypeEnum.ZHL.getType(), getUserType().getValue());
        if (socialUserRespDTO == null) {
            log.info("zhlTicketLogin社交用户不存在, openid: {}", reqVO.getOpenid());
            throw exception(ZHL_TICKET_LOGIN_ERROR);
        }
        MemberUserDO user = userService.getUser(socialUserRespDTO.getUserId());
        if (user == null) {
            log.info("zhlTicketLogin用户不存在, userId: {}", socialUserRespDTO.getUserId());
            throw exception(ZHL_TICKET_LOGIN_ERROR);
        }

        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user, user.getMobile(), LoginLogTypeEnum.LOGIN_SOCIAL, socialUserRespDTO.getOpenid());
    }
}
