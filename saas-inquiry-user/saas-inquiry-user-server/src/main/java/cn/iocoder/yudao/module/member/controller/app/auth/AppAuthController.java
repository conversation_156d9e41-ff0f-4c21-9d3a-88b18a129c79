package cn.iocoder.yudao.module.member.controller.app.auth;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.ZHL_TICKET_LOGIN_ERROR;
import static com.xyy.saas.inquiry.util.AESUtil.AESUtil_KEY;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.member.controller.app.auth.vo.AppAuthLoginRespVO;
import cn.iocoder.yudao.module.member.controller.app.auth.vo.AppAuthWeixinMiniAppLoginReqVO;
import cn.iocoder.yudao.module.member.controller.admin.auth.vo.ZhlBindAccountReqVO;
import cn.iocoder.yudao.module.member.controller.admin.auth.vo.ZhlBindAccountRespVO;
import cn.iocoder.yudao.module.member.controller.admin.auth.vo.ZhlBindTenantReqVO;
import cn.iocoder.yudao.module.member.controller.admin.auth.vo.ZhlBindTenantRespVO;
import cn.iocoder.yudao.module.member.controller.admin.auth.vo.ZhlTicketLoginReqVO;
import cn.iocoder.yudao.module.member.controller.admin.auth.vo.ZhlTicketVO;
import cn.iocoder.yudao.module.member.service.auth.MemberAuthService;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthLoginRespVO;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthLoginTenantReqVO;
import cn.iocoder.yudao.module.system.service.auth.AdminAuthService;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum;
import com.xyy.saas.inquiry.util.AESUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "用户 APP - 认证")
@RestController
@RequestMapping(value = {"admin-api/system/auth", "/app-api/system/auth"})
@Validated
@Slf4j
public class AppAuthController {

    @Resource
    private MemberAuthService authService;

    @Resource
    private AdminAuthService adminAuthService;

    @PostMapping("/weixin-mini-app-login")
    @PermitAll
    @Operation(summary = "微信小程序的一键登录")
    public CommonResult<AuthLoginRespVO> weixinMiniAppLogin(@RequestBody @Valid AppAuthWeixinMiniAppLoginReqVO reqVO) {
        AppAuthLoginRespVO loginRespVO = authService.weixinMiniAppLogin(reqVO);
        return success(adminAuthService.loginWithTenantId(AuthLoginTenantReqVO.builder()
            .token(loginRespVO.getAccessToken())
            .userId(loginRespVO.getUserId())
            .tenantId(reqVO.getTenantId())
            .clientChannelType(ClientChannelTypeEnum.MINI_PROGRAM.getCode())
            .build()));
    }

    @PostMapping("/zhl-bind-tenant")
    @PermitAll
    @Operation(summary = "智慧脸绑定问诊门店")
    public CommonResult<ZhlBindTenantRespVO> zhlBindTenant(@RequestBody @Valid ZhlBindTenantReqVO reqVO) {
        return success(authService.zhlBindTenant(reqVO));
    }

    @PostMapping("/zhl-bind-account")
    @PermitAll
    @Operation(summary = "智慧脸绑定问诊账号")
    public CommonResult<ZhlBindAccountRespVO> zhlBindAccount(@RequestBody @Valid ZhlBindAccountReqVO reqVO) {
        return success(authService.zhlBindAccount(reqVO));
    }

    @PostMapping("/zhl-ticket-login")
    @PermitAll
    @Operation(summary = "智慧脸登录")
    public CommonResult<AuthLoginRespVO> zhlTicketLogin(@RequestBody @Valid ZhlTicketLoginReqVO reqVO) {

        log.info("zhlTicketLogin->reqVO:{}",JSONObject.toJSON(reqVO));
        String decrypt;
        try {
            decrypt  = AESUtil.decrypt(reqVO.getTicket(), AESUtil_KEY);
        }catch (Exception e){
            log.error("智慧脸登录失败#decrypt：{}, {}", JSONObject.toJSONString(reqVO), e.getMessage());
            throw exception(ZHL_TICKET_LOGIN_ERROR);
        }

        log.info("zhlTicketLogin->decrypt:{}",decrypt);

        ZhlTicketVO zhlTicketVO = JSONObject.parseObject(decrypt, ZhlTicketVO.class);
        if (zhlTicketVO == null || StringUtils.isBlank(zhlTicketVO.getOpenid()) || zhlTicketVO.getTenantId() == null) {
            log.info("智慧脸登录失败#参数缺失：{}, {}", JSONObject.toJSONString(reqVO), zhlTicketVO);
            throw exception(ZHL_TICKET_LOGIN_ERROR);
        }

        reqVO.setOpenid(zhlTicketVO.getOpenid());
        reqVO.setTenantId(zhlTicketVO.getTenantId());

        AppAuthLoginRespVO loginRespVO = authService.zhlTicketLogin(reqVO);

        return success(adminAuthService.loginWithTenantId(AuthLoginTenantReqVO.builder()
            .token(loginRespVO.getAccessToken())
            .userId(loginRespVO.getUserId())
            .tenantId(zhlTicketVO.getTenantId())
            .clientChannelType(ClientChannelTypeEnum.PC.getCode())
            .build()));
    }
}
