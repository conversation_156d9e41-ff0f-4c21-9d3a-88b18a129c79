<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.xyy.saas</groupId>
    <artifactId>saas-inquiry-signature</artifactId>
    <version>${revision}</version>
    <relativePath>../pom.xml</relativePath>
  </parent>
  <artifactId>saas-inquiry-signature-api</artifactId>
  <version>${revision}</version>
  <name>${project.artifactId}</name>
  <description>${project.artifactId}</description>
  <packaging>jar</packaging>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <java.version>21</java.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-pojo</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-common</artifactId>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>
