package com.xyy.saas.inquiry.signature.server.convert.signature;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.module.system.api.permission.dto.RoleRespDTO;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserSaveDTO;
import com.xyy.saas.inquiry.signature.server.controller.app.signature.vo.InquirySignatureRoleVO;
import com.xyy.saas.inquiry.signature.server.controller.app.signature.vo.InquiryUserSignatureInformationVO;
import com.xyy.saas.inquiry.signature.server.controller.app.signature.vo.InquiryUserSignatureManageVO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquiryUserSignatureInformationDO;
import java.util.HashSet;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author:chenxiaoyi
 * @Date:2024/10/12 17:17
 */
@Mapper
public interface InquiryUserSignatureConvert {

    InquiryUserSignatureConvert INSTANCE = Mappers.getMapper(InquiryUserSignatureConvert.class);

    @Mapping(target = "id", source = "userId")
    AdminUserSaveDTO convertUser(InquiryUserSignatureManageVO createReqVO);

    default InquiryUserSignatureManageVO convert(AdminUserRespDTO user, InquiryUserSignatureInformationDO informationDO) {
        return new InquiryUserSignatureManageVO()
            .setNickname(user.getNickname())
            .setSex(user.getSex())
            .setIdCard(user.getIdCard())
            .setMobile(user.getMobile())
            .setRoleIds(CollUtil.isEmpty(user.getRoleIds()) ? new HashSet<>() : new HashSet<>(user.getRoleIds()))
            .setRoleNames(user.getRoleNames())
            .setSignatureUrl(informationDO == null ? null : informationDO.getSignatureUrl());

    }

    InquiryUserSignatureInformationDO convertDo(InquiryUserSignatureInformationVO informationVO);

    InquiryUserSignatureInformationVO convertVo(InquiryUserSignatureInformationDO informationDO);

    List<InquirySignatureRoleVO> convertRole(List<RoleRespDTO> roleRespDTOS);
}
