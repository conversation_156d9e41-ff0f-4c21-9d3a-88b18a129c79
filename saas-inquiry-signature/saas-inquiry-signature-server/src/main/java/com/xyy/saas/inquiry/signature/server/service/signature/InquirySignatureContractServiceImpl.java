package com.xyy.saas.inquiry.signature.server.service.signature;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.signature.enums.ErrorCodeConstants.SIGNATURE_CONTRACT_NOT_EXISTS;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.enums.signature.ContractStatusEnum;
import com.xyy.saas.inquiry.enums.signature.ContractTypeEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionTemplateFieldDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureInitDto;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionTemplateField;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractAddParticipantVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractPageReqVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractSaveReqVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractStatusVO;
import com.xyy.saas.inquiry.signature.server.convert.prescription.InquiryPrescriptionSignatureConvert;
import com.xyy.saas.inquiry.signature.server.convert.prescriptiontemplate.PrescriptionTemplateConvert;
import com.xyy.saas.inquiry.signature.server.convert.signature.InquirySignatureContractConvert;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.prescriptiontemplate.InquiryPrescriptionTemplateDO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquirySignatureContractDO;
import com.xyy.saas.inquiry.signature.server.dal.mysql.signature.InquirySignatureContractMapper;
import com.xyy.saas.inquiry.signature.server.service.prescriptiontemplate.InquiryPrescriptionTemplateService;
import com.xyy.saas.inquiry.signature.server.service.prescriptiontemplate.dto.TemplateSignCheckedDto;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 签章合同 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InquirySignatureContractServiceImpl implements InquirySignatureContractService {

    @Resource
    private InquirySignatureContractMapper signatureContractMapper;

    @Resource
    @Lazy
    private InquiryPrescriptionTemplateService prescriptionTemplateService;

    @Override
    public InquirySignatureContractDO querySignatureContract(InquirySignatureContractSaveReqVO createReqVO) {
        return signatureContractMapper.selectOne(createReqVO);
    }

    @Override
    public InquirySignatureContractDO querySignatureContractByThirdId(String thirdId, SignaturePlatformEnum signaturePlatformEnum) {
        return signatureContractMapper.selectOne(InquirySignatureContractSaveReqVO.builder().thirdId(thirdId).signaturePlatform(signaturePlatformEnum.getCode()).build());
    }

    @Override
    public InquirySignatureContractDO querySignatureContractByBizId(String bizId, ContractTypeEnum contractTypeEnum) {
        return signatureContractMapper.selectOne(InquirySignatureContractSaveReqVO.builder().bizId(bizId).contractType(contractTypeEnum.getCode()).build());
    }

    @Override
    public InquirySignatureContractDO saveOrGetSignatureContractByCondition(InquirySignatureContractSaveReqVO createReqVO) {

        InquirySignatureContractDO signatureContractDO = signatureContractMapper.selectOne(
            InquirySignatureContractSaveReqVO.builder().bizId(createReqVO.getBizId()).contractType(createReqVO.getContractType()).thirdId(createReqVO.getThirdId()).signaturePlatform(createReqVO.getSignaturePlatform()).build());
        if (signatureContractDO != null) {
            return signatureContractDO;
        }
        // 插入
        InquirySignatureContractDO signatureContract = InquirySignatureContractConvert.INSTANCE.convertDo(createReqVO);
        signatureContractMapper.insert(signatureContract);
        return signatureContract;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSignatureContractStatus(InquirySignatureContractStatusVO signatureContractStatusVO) {
        InquirySignatureContractDO signatureContractDO = getSignatureContractByPref(signatureContractStatusVO.getPref());
        if (signatureContractDO == null) {
            throw exception(SIGNATURE_CONTRACT_NOT_EXISTS);
        }
        InquirySignatureContractDO signatureContract = InquirySignatureContractConvert.INSTANCE.convertStatusVo(signatureContractStatusVO);
        return signatureContractMapper.updateById(signatureContract.setId(signatureContractDO.getId())) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InquirySignatureContractDO appendParticipantItem(InquirySignatureContractAddParticipantVO addParticipantVO) {
        InquirySignatureContractDO signatureContractDO = querySignatureContractByBizId(addParticipantVO.getBizId(), ContractTypeEnum.fromStatusCode(addParticipantVO.getContractType()));
        if (signatureContractDO == null) {
            throw exception(SIGNATURE_CONTRACT_NOT_EXISTS);
        }

        // 获取处方笺模板下一级参与方
        InquiryPrescriptionTemplateDO prescriptionTemplateDO = prescriptionTemplateService.validateInquiryPrescriptionTemplateExists(signatureContractDO.getTemplateIdLong());

        TemplateSignCheckedDto checkedDto = PrescriptionTemplateConvert.INSTANCE.convertAppendPart(prescriptionTemplateDO, signatureContractDO, addParticipantVO);
        PrescriptionTemplateFieldDto nextTemplateField = prescriptionTemplateService.getSignNextTemplateFieldValidUrl(checkedDto);
        if (nextTemplateField == null) {
            signatureContractDO.setContractStatus(ContractStatusEnum.COMPLETE.getCode()); // 已经没有下一级了,修改状态为已完成
        }
        // 获取参与方签章图片
        InquiryPrescriptionSignatureConvert.INSTANCE.fillParticipant(addParticipantVO.getParticipantItem(), nextTemplateField);
        signatureContractDO.getParticipants().add(addParticipantVO.getParticipantItem());
        signatureContractMapper.updateById(signatureContractDO);
        return signatureContractDO;
    }

    @Override
    public Long createSignatureContract(InquirySignatureContractSaveReqVO createReqVO) {
        // 插入
        InquirySignatureContractDO signatureContract = BeanUtils.toBean(createReqVO, InquirySignatureContractDO.class);
        signatureContractMapper.insert(signatureContract);
        // 返回
        return signatureContract.getId();
    }

    @Override
    public void updateSignatureContract(InquirySignatureContractSaveReqVO updateReqVO) {
        // 校验存在
        validateSignatureContractExists(updateReqVO.getId());
        // 更新
        InquirySignatureContractDO updateObj = BeanUtils.toBean(updateReqVO, InquirySignatureContractDO.class);
        signatureContractMapper.updateById(updateObj);
    }

    @Override
    public void deleteSignatureContract(Long id) {
        // 校验存在
        validateSignatureContractExists(id);
        // 删除
        signatureContractMapper.deleteById(id);
    }

    @Override
    public InquirySignatureContractDO validateSignatureContractExists(Long id) {
        final InquirySignatureContractDO signatureContractDO = signatureContractMapper.selectById(id);
        if (signatureContractDO == null) {
            throw exception(SIGNATURE_CONTRACT_NOT_EXISTS);
        }
        return signatureContractDO;
    }

    @Override
    public InquirySignatureContractDO validateSignatureContractPrefExists(String pref) {
        final InquirySignatureContractDO signatureContractDO = getSignatureContractByPref(pref);
        if (signatureContractDO == null) {
            throw exception(SIGNATURE_CONTRACT_NOT_EXISTS);
        }
        return signatureContractDO;
    }

    @Override
    public InquirySignatureContractDO getSignatureContract(Long id) {
        return signatureContractMapper.selectById(id);
    }

    @Override
    public InquirySignatureContractDO getSignatureContractByPref(String pref) {
        return signatureContractMapper.selectOne(InquirySignatureContractSaveReqVO.builder().pref(pref).build());
    }

    @Override
    public PageResult<InquirySignatureContractDO> getSignatureContractPage(InquirySignatureContractPageReqVO pageReqVO) {
        return signatureContractMapper.selectPage(pageReqVO);
    }

}