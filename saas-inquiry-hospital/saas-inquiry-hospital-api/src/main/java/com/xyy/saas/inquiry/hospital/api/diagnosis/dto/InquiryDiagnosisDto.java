package com.xyy.saas.inquiry.hospital.api.diagnosis.dto;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:chenxiaoyi
 * @Date:2024/10/18 17:51
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquiryDiagnosisDto extends PageParam {

    private Long id;
    /**
     * 诊断编码
     */
    private String diagnosisCode;
    /**
     * 诊断名称
     */
    private String diagnosisName;
    /**
     * 诊断类型：0-默认(西医),1-中医
     */
    private Integer diagnosisType;
    /**
     * 展示诊断名称
     */
    private String showName;
    /**
     * 状态 0启用 1禁用
     */
    private Integer status;
    /**
     * 性别限制：0无限制,1限男,2限女
     */
    private Integer sexLimit;

    /**
     * 数据类型：1-常规, 2-系统默认 3-推荐
     */
    private Integer dataType;

    private List<Long> ids;

    private List<Long> noIds;

    private List<String> diagnosisCodes;

    private List<String> showNames;

    private List<Integer> diagnosisTypes;

    private List<Integer> sexLimits;

}
