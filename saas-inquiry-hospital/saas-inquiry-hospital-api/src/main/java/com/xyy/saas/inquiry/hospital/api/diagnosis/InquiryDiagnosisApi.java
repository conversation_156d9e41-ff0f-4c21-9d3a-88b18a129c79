package com.xyy.saas.inquiry.hospital.api.diagnosis;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDepartmentRelationDto;
import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDepartmentRelationReqDto;
import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDto;
import java.util.List;

/**
 * 诊断服务api
 *
 * @Author:chenxiaoyi
 * @Date:2024/09/30 10:45
 */
public interface InquiryDiagnosisApi {

    /**
     * 根据条件查询关联诊断科室
     *
     * @param req
     * @return 关联诊断科室
     */
    List<InquiryDiagnosisDepartmentRelationDto> queryDiagnosisDepartmentRelation(
        InquiryDiagnosisDepartmentRelationReqDto req);


    List<InquiryDiagnosisDto> queryDiagnosisByCondition(List<String> showNames);

    /**
     * 分页查询诊断信息（用于字典匹配）
     *
     * @param pageReqDto 分页查询参数
     * @return 分页结果
     */
    PageResult<InquiryDiagnosisDto> getDiagnosisPage(InquiryDiagnosisDto pageReqDto);

    /**
     * 根据诊断名称模糊查询诊断列表（用于字典匹配）
     *
     * @param diagnosisName 诊断名称
     * @return 诊断列表
     */
    List<InquiryDiagnosisDto> getDiagnosisByName(String diagnosisName);

    /**
     * 根据ID列表查询诊断信息（用于字典匹配）
     *
     * @param ids 诊断ID列表
     * @return 诊断列表
     */
    List<InquiryDiagnosisDto> getDiagnosisByIds(List<Long> ids);
}
