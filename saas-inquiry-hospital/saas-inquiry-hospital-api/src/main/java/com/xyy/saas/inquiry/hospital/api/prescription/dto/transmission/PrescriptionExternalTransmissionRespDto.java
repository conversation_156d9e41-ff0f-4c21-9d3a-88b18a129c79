package com.xyy.saas.inquiry.hospital.api.prescription.dto.transmission;

import com.xyy.saas.inquiry.pojo.BaseDto;
import com.xyy.saas.inquiry.pojo.medicare.MedicareSignInDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;


/**
 * 处方外配 TransmissionDto
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrescriptionExternalTransmissionRespDto extends BaseDto {


    /**
     * 定点医疗机构编号
     */
    private String fixMedicalInstitutionsCode;

    /**
     * 定点医疗机构名称
     */
    private String fixMedicalInstitutionsName;


    /**
     * 三方系统处方号
     */
    private String externalRxPref;


    /**
     * 电子处方平台流水号
     */
    private String electronicRxSn;

    /**
     * 处方追溯码
     */
    private String medicareRxNo;

    /**
     * 医保处方编号
     */
    private String medicareRxTraceCode;

    @Schema(description = "电子处方审核意见")
    private String rxChkOpinions;

    @Schema(description = "医保电子签名后处方信息originalValue的签名结果值")
    private String signDigest;

    @Schema(description = "签名机构证书SN")
    private String signCertSn;

    @Schema(description = "签名机构证书DN")
    private String signCertDn;


    /**
     * 医保签到信息
     */
    private MedicareSignInDto medicareSigninExt;


}