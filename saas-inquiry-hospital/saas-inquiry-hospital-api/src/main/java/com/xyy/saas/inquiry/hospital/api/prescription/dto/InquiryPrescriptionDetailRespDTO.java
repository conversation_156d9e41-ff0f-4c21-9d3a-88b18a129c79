package com.xyy.saas.inquiry.hospital.api.prescription.dto;

import com.xyy.saas.inquiry.pojo.BaseDto;
import com.xyy.saas.inquiry.product.api.catalog.dto.MedicalCatalogDetailDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Author:chenxiaoyi
 * @Date:2024/12/03 19:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryPrescriptionDetailRespDTO extends BaseDto {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "2494")
    private Long id;

    @Schema(description = "处方编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String prescriptionPref;

    @Schema(description = "问诊单pref", requiredMode = Schema.RequiredMode.REQUIRED)
    private String inquiryPref;

    private Long tenantId;

    @Schema(description = "租户名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    private String tenantName;

    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productPref;

    @Schema(description = "标准库id", requiredMode = Schema.RequiredMode.REQUIRED, example = "8400")
    private String standardId;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String productName;

    @Schema(description = "通用名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String commonName;

    @Schema(description = "用药方法eg:口服", requiredMode = Schema.RequiredMode.REQUIRED)
    private String directions;

    @Schema(description = "单次剂量 eg:1", requiredMode = Schema.RequiredMode.REQUIRED)
    private String singleDose;

    @Schema(description = "单次剂量单位 eg:片", requiredMode = Schema.RequiredMode.REQUIRED)
    private String singleUnit;

    @Schema(description = "使用频率 eg:一日三次", requiredMode = Schema.RequiredMode.REQUIRED)
    private String useFrequency;

    @Schema(description = "药品类型：0西药，1中药", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer medicineType;

    @Schema(description = "数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal quantity;

    @Schema(description = "商品规格", requiredMode = Schema.RequiredMode.REQUIRED)
    private String attributeSpecification;

    @Schema(description = "生产厂家", requiredMode = Schema.RequiredMode.REQUIRED)
    private String manufacturer;

    @Schema(description = "产地", requiredMode = Schema.RequiredMode.REQUIRED)
    private String producingArea;

    @Schema(description = "批准文号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String approvalNumber;

    @Schema(description = "剂型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String dosageForm;

    @Schema(description = "处方分类 甲类-乙类OTC,处方药", requiredMode = Schema.RequiredMode.REQUIRED)
    private String presCategory;

    @Schema(description = "包装单位名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String packageUnit;

    @Schema(description = "商品系统类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer productSystemType;

    @Schema(description = "是否处方药:0否，1是")
    private Integer prescriptionYn;

    @Schema(description = "商品价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "8211")
    private BigDecimal productPrice;

    @Schema(description = "实收金额", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal actualAmount;

    /**
     * 医保目录编码
     */
    private MedicalCatalogDetailDTO medicalCatalogDetail;

}
