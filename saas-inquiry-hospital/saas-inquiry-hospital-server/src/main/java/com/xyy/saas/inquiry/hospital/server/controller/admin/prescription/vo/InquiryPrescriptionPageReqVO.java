package com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.xyy.saas.inquiry.enums.inquiry.QuerySourceEnum;
import com.xyy.saas.inquiry.hospital.server.constant.QuerySceneEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 处方记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryPrescriptionPageReqVO extends PageParam {

    @Schema(description = "处方ids")
    private List<Long> ids;

    private String pref;

    private List<String> prefs;

    @Schema(description = "租户ID", example = "1517")
    private Long tenantId;

    /**
     * 目前门店id
     */
    private Long targetTenantId;

    @Schema(description = "租户IDs", example = "1517")
    private List<Long> tenantIds;

    @Schema(description = "患者姓名", example = "30337")
    private String patientName;

    @Schema(description = "患者编码", example = "30337")
    private String patientPref;

    @Schema(description = "医院编码")
    private String hospitalPref;

    @Schema(description = "药师编码")
    private String pharmacistPref;

    @Schema(description = "医生编码", example = "20472")
    private String doctorPref;

    /**
     * {@link com.xyy.saas.inquiry.enums.inquiry.PrescriptionStatusEnum}
     */
    @Schema(description = "处方状态  0、待开方   1、已取消   2、待审核     3、审核中  4、审核通过   5、审核驳回", example = "2")
    private Integer status;

    private List<Integer> statuss;

    /**
     * {@link com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum}
     */
    @Schema(description = "当前审方人类型 1-医生,2-药店,3-平台,4-医院", example = "2")
    private Integer auditorType;

    @Schema(description = "处方分发状态 0-未分配,1-已分配", example = "2")
    private Integer distributeStatus;

    @Schema(description = "处方分配的用户id", example = "2")
    private Long distributeUserId;


    @Schema(description = "用药类型：0西药，1中药", example = "2")
    private Integer medicineType;

    @Schema(description = "用药类型：0西药，1中药", example = "2")
    private List<Integer> medicineTypes;

    @Schema(description = "药师审方时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] auditPrescriptionTime;

    @Schema(description = "医生开方时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] outPrescriptionTime;

    @Schema(description = "问诊方式  1、图文问诊  2、视频问诊  3、电话问诊", example = "1")
    private Integer inquiryWayType;

    @Schema(description = "问诊业务类型 1、药店问诊  2、远程审方", example = "1")
    private Integer inquiryBizType;

    @Schema(description = "客户端渠类型 0、app  1、pc  2、小程序 ", example = "1")
    private Integer clientChannelType;

    @Schema(description = "问诊渠道 0、荷叶 1、智慧脸  2、海典ERP", example = "2")
    private Integer bizChannelType;

    @Schema(description = "处方打印状态（0-未打印、1-已打印、NULL -未知）", example = "2")
    private Integer printStatus;

    @Schema(description = "是否自动开方（0-否  1是）", example = "0")
    private Integer autoInquiry;

    @Schema(description = "三方处方编号", example = "0")
    private String thirdPrescriptionNo;


    @Schema(description = "问诊编码列表", example = "0")
    private List<String> inquiryPrefList;


    /**
     * {@link QuerySceneEnum }
     */
    @Schema(description = "数据查询场景")
    @NotNull(message = "数据查询场景不能为空")
    private Integer queryScene;


    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    /**
     * 数据状态 0 有效 1 作废 {@link CommonStatusEnum}
     */
    private Integer enable;

    /**
     * {@link QuerySourceEnum }
     */
    @Schema(description = "查询类型")
    private QuerySourceEnum querySource;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "处方类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer prescriptionType;


    public void isAppQuery() {
        this.querySource = QuerySourceEnum.APP;
    }

    public void isWebQuery() {
        this.querySource = QuerySourceEnum.WEB;
    }

    /**
     * 是否是医院员工查询
     */
    private boolean hospitalEmployee;

    /**
     * 医院编码
     */
    private List<String> hospitalList;
}