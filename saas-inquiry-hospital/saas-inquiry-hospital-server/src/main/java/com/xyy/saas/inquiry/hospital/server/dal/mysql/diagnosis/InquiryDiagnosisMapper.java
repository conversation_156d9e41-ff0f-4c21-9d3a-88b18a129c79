package com.xyy.saas.inquiry.hospital.server.dal.mysql.diagnosis;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.diagnosis.InquiryDiagnosisDO;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;

/**
 * 问诊诊断信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryDiagnosisMapper extends BaseMapperX<InquiryDiagnosisDO> {

    default PageResult<InquiryDiagnosisDO> selectPage(InquiryDiagnosisPageReqVO reqVO) {
        LambdaQueryWrapperX<InquiryDiagnosisDO> queryWrapper = new LambdaQueryWrapperX<InquiryDiagnosisDO>()
            .inIfPresent(InquiryDiagnosisDO::getId, reqVO.getIds())
            .eqIfPresent(InquiryDiagnosisDO::getDiagnosisCode, reqVO.getDiagnosisCode())
            .likeIfPresent(InquiryDiagnosisDO::getDiagnosisName, reqVO.getDiagnosisName())
            .eqIfPresent(InquiryDiagnosisDO::getDiagnosisType, reqVO.getDiagnosisType())
            .likeIfPresent(InquiryDiagnosisDO::getShowName, reqVO.getShowName())
            .eqIfPresent(InquiryDiagnosisDO::getStatus, reqVO.getStatus())
            .eqIfPresent(InquiryDiagnosisDO::getSexLimit, reqVO.getSexLimit())
            .eqIfPresent(InquiryDiagnosisDO::getDataType, reqVO.getDataType())
            .betweenIfPresent(InquiryDiagnosisDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(InquiryDiagnosisDO::getId);

        if (StringUtils.isNotBlank(reqVO.getNameCode())) {
            queryWrapper.and(orWrapper -> orWrapper.or(o -> o.like(InquiryDiagnosisDO::getShowName, reqVO.getNameCode()))
                .or(a -> a.like(InquiryDiagnosisDO::getDiagnosisCode, reqVO.getNameCode())));
        }
        queryWrapper.notIn(CollUtil.isNotEmpty(reqVO.getNoIds()), InquiryDiagnosisDO::getId, reqVO.getNoIds());

        return selectPage(reqVO, queryWrapper);
    }


    default List<InquiryDiagnosisDO> queryByCondition(InquiryDiagnosisDto diagnosisDto) {
        return selectList(new LambdaQueryWrapperX<InquiryDiagnosisDO>()
            .inIfPresent(InquiryDiagnosisDO::getId, diagnosisDto.getIds())
            .eqIfPresent(InquiryDiagnosisDO::getDiagnosisCode, diagnosisDto.getDiagnosisCode())
            .eqIfPresent(InquiryDiagnosisDO::getDiagnosisName, diagnosisDto.getDiagnosisName())
            .eqIfPresent(InquiryDiagnosisDO::getDiagnosisType, diagnosisDto.getDiagnosisType())
            .likeIfPresent(InquiryDiagnosisDO::getShowName, diagnosisDto.getShowName())
            .eqIfPresent(InquiryDiagnosisDO::getStatus, diagnosisDto.getStatus())
            .eqIfPresent(InquiryDiagnosisDO::getSexLimit, diagnosisDto.getSexLimit())
            .eqIfPresent(InquiryDiagnosisDO::getDataType, diagnosisDto.getDataType())
            .inIfPresent(InquiryDiagnosisDO::getDiagnosisType, diagnosisDto.getDiagnosisTypes())
            .inIfPresent(InquiryDiagnosisDO::getDiagnosisCode, diagnosisDto.getDiagnosisCodes())
            .inIfPresent(InquiryDiagnosisDO::getShowName, diagnosisDto.getShowNames())
            .inIfPresent(InquiryDiagnosisDO::getSexLimit, diagnosisDto.getSexLimit())
            .orderByDesc(InquiryDiagnosisDO::getUpdateTime));
    }


    default List<InquiryDiagnosisDO> queryInquiryDiagnosisByShowName(String showName, Integer status) {
        return selectList(new LambdaQueryWrapperX<InquiryDiagnosisDO>()
            .eqIfPresent(InquiryDiagnosisDO::getStatus, status)
            .and((q) -> q.like(InquiryDiagnosisDO::getShowName, showName)
                .or().like(InquiryDiagnosisDO::getDiagnosisCode, showName)));
    }
}