package com.xyy.saas.inquiry.hospital.server.service.message;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.drugstore.api.option.InquiryOptionConfigApi;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantParamConfigApi;
import com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum;
import com.xyy.saas.inquiry.enums.im.ImEventPushEnum;
import com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorCardInfoDto;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorPageReqVO;
import com.xyy.saas.inquiry.hospital.server.convert.doctor.InquiryDoctorImConvert;
import com.xyy.saas.inquiry.hospital.server.convert.prescription.InquiryPrescriptionImConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.clinicalcase.InquiryClinicalCaseDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorService;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalService;
import com.xyy.saas.inquiry.im.api.message.InquiryImMessageApi;
import com.xyy.saas.inquiry.im.api.message.InquiryNotificationPushApi;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryImMessageDto;
import com.xyy.saas.inquiry.im.api.user.InquiryImUserApi;
import com.xyy.saas.inquiry.im.enums.PushContentEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.util.ThreadPoolManager;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * @Author:chenxiaoyi
 * @Date:2025/03/05 14:38
 */
@Service
@Slf4j
public class DoctorImServiceImpl implements DoctorImService {

    @Resource
    private InquiryImUserApi inquiryImUserApi;

    @Resource
    private InquiryImMessageApi inquiryImMessageApi;

    @Resource
    private InquiryHospitalService inquiryHospitalService;

    @Resource
    private InquiryDoctorService inquiryDoctorService;

    @Resource
    private InquiryOptionConfigApi inquiryOptionConfigApi;

    @Resource
    private TenantApi tenantApi;

    @Resource
    private TenantParamConfigApi tenantParamConfigApi;

    @DubboReference
    private InquiryApi inquiryApi;

    @DubboReference
    private InquiryNotificationPushApi inquiryNotificationPushApi;

    @Override
    public void sendPrescriptionImMessage(InquiryPrescriptionRespDTO prescriptionRespDTO, String inquiryPref, ImEventPushEnum eventEnum) {
        InquiryRecordDto inquiryRecordDto = inquiryApi.getInquiryDtoByPref(inquiryPref);
        if (inquiryRecordDto == null) {
            log.error("问诊单号：{}，发送im消息查询问诊单失败", inquiryPref);
            return;
        }
        ThreadPoolManager.execute(() -> {
            try {
                // 获取商家IM账号
                String patientIm = inquiryImUserApi.getImAccountByUserIdAndClientType(Long.valueOf(inquiryRecordDto.getCreator()), inquiryRecordDto.getClientChannelType());
                if (inquiryRecordDto.isTextInquiry()) {
                    // 查询日期
                    Integer dateType = tenantParamConfigApi.getTenantPresDateType(inquiryRecordDto.getTenantId());
                    // 获取医生卡片信息
                    InquiryDoctorCardInfoDto doctorCardInfoDto = inquiryDoctorService.getDoctorCardInfoByDoctorPref(inquiryRecordDto.getDoctorPref());
                    // 查询接诊医院信息
                    InquiryHospitalRespDto hospitalDto = inquiryHospitalService.getInquiryHospital(inquiryRecordDto.getHospitalPref());
                    // 获取医生IM账号
                    String doctorIm = inquiryImUserApi.getDoctorImAccountByDoctorPref(inquiryRecordDto.getDoctorPref());
                    // 是否需要针对患者隐藏
                    boolean isHide = needHidePrescription(inquiryRecordDto);
                    // 生成开具处方消息
                    List<InquiryImMessageDto> messageDtos = InquiryPrescriptionImConvert.INSTANCE.convertPrescriptionMessageList(prescriptionRespDTO, inquiryRecordDto, doctorIm, patientIm, doctorCardInfoDto, hospitalDto, isHide, dateType);
                    // 循环发送消息
                    for (InquiryImMessageDto messageDto : messageDtos) {
                        inquiryImMessageApi.sendUserMessage(messageDto);
                    }
                }
                // 处方开具场景的消息放在签章回调发送
                if (ObjectUtil.isNotEmpty(prescriptionRespDTO)) {
                    return;
                }
                // 发送消息通知商家 医生取消开方  或  处方开具超时
                InquiryImMessageDto messageDto = InquiryDoctorImConvert.INSTANCE.convertSystemMsgForInquiryEndToPatient(inquiryRecordDto.getPref(), patientIm, eventEnum);
                // 发送系统消息通知商家端
                inquiryImMessageApi.sendSystemMessage(messageDto);
            } catch (Exception e) {
                log.error("问诊单号：{}，发送问诊结束消息失败，原因：{}", inquiryRecordDto.getPref(), e);
            }
        });
    }

    /**
     * 判断处方是否需要隐藏
     *
     * @param inquiryRecordDto
     * @return
     */
    private boolean needHidePrescription(InquiryRecordDto inquiryRecordDto) {
        // 非小程序问诊无需隐藏
        if (ObjectUtil.notEqual(inquiryRecordDto.getClientChannelType(), ClientChannelTypeEnum.MINI_PROGRAM.getCode())) {
            return Boolean.FALSE;
        }
        TenantDto tenantDto = tenantApi.getTenant(inquiryRecordDto.getTenantId());
        // 小程序问诊需要判断当前商家是否需要隐藏处方
        InquiryOptionConfigRespDto inquiryOptionConfig = inquiryOptionConfigApi.getInquiryOptionConfig(tenantDto, InquiryOptionTypeEnum.PROC_WE_CHAT_INQUIRY_PATIENT_VIEW_PRESCRIPTION);
        if(ObjectUtil.isNotEmpty(inquiryOptionConfig) && ObjectUtil.isNotEmpty(inquiryOptionConfig.getProcWeChatInquiryPatientViewPrescription()) && ObjectUtil.equals(inquiryOptionConfig.getProcWeChatInquiryPatientViewPrescription(), Boolean.TRUE)){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


    @Override
    public void sendClinicalCaseImMessage(InquiryClinicalCaseDO clinicalCaseDO, InquiryRecordDto inquiryRecordDto) {
        ThreadPoolManager.execute(() -> {
            try {
                // 查询日期
                Integer dateType = tenantParamConfigApi.getTenantPresDateType(inquiryRecordDto.getTenantId());
                // 获取医生IM账号
                String doctorIm = inquiryImUserApi.getDoctorImAccountByDoctorPref(inquiryRecordDto.getDoctorPref());
                // 获取商家IM账号
                String patientIm = inquiryImUserApi.getImAccountByUserIdAndClientType(Long.valueOf(inquiryRecordDto.getCreator()), inquiryRecordDto.getClientChannelType());
                // 发送IM消息
                InquiryImMessageDto messageDto = InquiryPrescriptionImConvert.INSTANCE.convertClinicalCaseMessage(clinicalCaseDO, inquiryRecordDto.getStartTime(), doctorIm, patientIm, dateType);
                inquiryImMessageApi.sendUserMessage(messageDto);
            } catch (Exception e) {
                log.error("问诊单号：{},发送门诊病例消息失败,原因：{}", inquiryRecordDto.getPref(), e);
            }
        });


    }

    @Override
    public void sendDoctorReviewsImMessage(String inquiryPref) {
        ThreadPoolManager.execute(() -> {
            try {
                // 查询问诊单详情
                InquiryRecordDto inquiryRecordDto = inquiryApi.getInquiryRecord(inquiryPref);
                // 获取医生IM账号
                String doctorIm = inquiryImUserApi.getDoctorImAccountByDoctorPref(inquiryRecordDto.getDoctorPref());
                // 发送消息通知商家处理
                InquiryImMessageDto messageDto = InquiryDoctorImConvert.INSTANCE.convertSystemMsgToDoctor(doctorIm, ImEventPushEnum.ISSUE_PRESCRIPTION_EVALUATE, inquiryPref);
                // 发送系统消息通知商家端
                inquiryImMessageApi.sendSystemMessage(messageDto);
            } catch (Exception e) {
                log.error("问诊单号：{}，发送医生评价消息失败，原因：{}", inquiryPref, e);
            }
        });
    }

    @Override
    public void batchNotifyDoctorForInquiryChange(List<String> doctorList) {
        inquiryApi.batchNotifyDoctorForInquiryChange(doctorList);
    }

    /**
     * 批量推送通知医生
     *
     * @param doctorList      医生列表
     * @param pushContentEnum 推送内容枚举
     * @param inquiryDto      问诊单信息
     */
    @Override
    public void batchPushNotifyMessage(List<String> doctorList, PushContentEnum pushContentEnum, InquiryRecordDto inquiryDto) {
        // 查询医生userId
        List<InquiryDoctorDO> inquiryDoctorList = inquiryDoctorService.getInquiryDoctorList(InquiryDoctorPageReqVO.builder().doctorPrefs(doctorList).build());
        if (CollectionUtils.isEmpty(inquiryDoctorList)) {
            return;
        }
        // 推送通知消息
        inquiryNotificationPushApi.sendNoticeToMobile(InquiryDoctorImConvert.INSTANCE.convertNotificationPushDto(inquiryDoctorList.stream().map(InquiryDoctorDO::getUserId).distinct().toList(), pushContentEnum, inquiryDto));
    }
}
