package com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorRespVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * 医生信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryDoctorMapper extends BaseMapperX<InquiryDoctorDO> {

    default PageResult<InquiryDoctorDO> selectPage(InquiryDoctorPageReqVO reqVO) {
        // 陕西监管备案状态查询 0:未备案 1:已备案
        return selectPage(reqVO, additionalQuery(new LambdaQueryWrapperX<>(), reqVO)
            .eqIfPresent(InquiryDoctorDO::getPref, reqVO.getPref())
            .inIfPresent(InquiryDoctorDO::getId, reqVO.getDoctorIds())
            .likeIfPresent(InquiryDoctorDO::getName, reqVO.getName())
            .eqIfPresent(InquiryDoctorDO::getSex, reqVO.getSex())
            .eqIfPresent(InquiryDoctorDO::getIdCard, reqVO.getIdCard())
            .eqIfPresent(InquiryDoctorDO::getMobile, reqVO.getMobile())
            .eqIfPresent(InquiryDoctorDO::getUserId, reqVO.getUserId())
            .eqIfPresent(InquiryDoctorDO::getAuditStatus, reqVO.getAuditStatus())
            .eqIfPresent(InquiryDoctorDO::getOnlineStatus, reqVO.getOnlineStatus())
            .eqIfPresent(InquiryDoctorDO::getCooperation, reqVO.getCooperation())
            .betweenIfPresent(InquiryDoctorDO::getStartInquiryTime, reqVO.getStartInquiryTime())
            .betweenIfPresent(InquiryDoctorDO::getEndInquiryTime, reqVO.getEndInquiryTime())
            .eqIfPresent(InquiryDoctorDO::getEnvTag, reqVO.getEnvTag())
            .eqIfPresent(InquiryDoctorDO::getPhoto, reqVO.getPhoto())
            .eqIfPresent(InquiryDoctorDO::getBiography, reqVO.getBiography())
            .eqIfPresent(InquiryDoctorDO::getProfessionalDec, reqVO.getProfessionalDec())
            .eqIfPresent(InquiryDoctorDO::getJobType, reqVO.getJobType())
            .eqIfPresent(InquiryDoctorDO::getPrescriptionPasswordStatus, reqVO.getPrescriptionPasswordStatus())
            .eqIfPresent(InquiryDoctorDO::getPrescriptionPassword, reqVO.getPrescriptionPassword())
            .eqIfPresent(InquiryDoctorDO::getDisable, reqVO.getDisable())
            .orderByDesc(InquiryDoctorDO::getId));
    }

    IPage<InquiryDoctorRespVO> selectDoctorPage(Page<InquiryDoctorPageReqVO> objectPage, @Param("reqVo") InquiryDoctorPageReqVO pageReqVO);


    /**
     * TODO 暂时先根据医生姓名模糊查询，后面再加条件，索引  等等
     *
     * @param reqVO
     * @return
     */
    default List<InquiryDoctorDO> selectList(InquiryDoctorPageReqVO reqVO) {
        LambdaQueryWrapperX<InquiryDoctorDO> wrapper = new LambdaQueryWrapperX<InquiryDoctorDO>()
            .inIfPresent(InquiryDoctorDO::getPref, reqVO.getDoctorPrefs())
            .eqIfPresent(InquiryDoctorDO::getCooperation, reqVO.getCooperation())
            .eqIfPresent(InquiryDoctorDO::getDisable, reqVO.getDisable())
            .likeIfPresent(InquiryDoctorDO::getMobile, reqVO.getMobile())
            .likeIfPresent(InquiryDoctorDO::getName, reqVO.getName());

        // 陕西监管备案状态查询 0:未备案 1:已备案
        additionalQuery(wrapper, reqVO);

        return selectList(wrapper);
    }

    /**
     * 陕西监管备案状态查询 0:未备案 1:已备案
     * @param wrapper
     * @param reqVO
     * @return
     */
    private LambdaQueryWrapperX<InquiryDoctorDO> additionalQuery(LambdaQueryWrapperX<InquiryDoctorDO> wrapper, InquiryDoctorPageReqVO reqVO) {
        Optional.ofNullable(reqVO.getFillingStatus4ShaanxiRegulatory()).ifPresent(status -> {
            if (status == 0) {
                wrapper.apply("JSON_EXTRACT(ext, '$.fillingStatus4ShaanxiRegulatory') = 0 or JSON_EXTRACT(ext, '$.fillingStatus4ShaanxiRegulatory') is null");
            } else {
                wrapper.apply("JSON_EXTRACT(ext, '$.fillingStatus4ShaanxiRegulatory') = {0}", status);
            }
        });

        return wrapper;
    }

}