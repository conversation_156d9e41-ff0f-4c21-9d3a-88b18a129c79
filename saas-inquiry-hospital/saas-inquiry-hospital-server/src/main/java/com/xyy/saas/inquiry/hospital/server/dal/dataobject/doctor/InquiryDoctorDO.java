package com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorExtDto;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 医生信息 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_inquiry_doctor", autoResultMap = true)
@KeySequence("saas_inquiry_doctor_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryDoctorDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 医生编码
     */
    private String pref;

    /**
     * 医生名称
     */
    private String name;
    /**
     * 性别 1男 2女
     */
    private Integer sex;
    /**
     * 身份证号码
     */
    private String idCard;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 审核状态 0、待审核  1、审核通过  2、审核驳回
     */
    private Integer auditStatus;

    /**
     * 是否自动抢单  0、否 1、是
     */
    private Integer autoGrabStatus;

    /**
     * 合作状态：0未合作 1 合作中 2禁用合作 3过期
     */
    private Integer cooperation;

    /**
     * 在线状态：0离线  1在线
     */
    private Integer onlineStatus;
    /**
     * 开始接诊时间
     */
    private LocalDateTime startInquiryTime;
    /**
     * 结束停诊时间
     */
    private LocalDateTime endInquiryTime;
    /**
     * 环境标志：prod-真实数据；test-测试数据；show-线上演示数据
     */
    private String envTag;
    /**
     * 证件照地址
     */
    private String photo;
    /**
     * 个人简介
     */
    private String biography;
    /**
     * 擅长专业,eg:擅长神经内科诊疗
     */
    private String professionalDec;
    /**
     * 医生类型： 1全职医生 2兼职医生
     */
    private Integer jobType;
    /**
     * 是否开启密码,0:不开启,1:开启
     */
    private Boolean prescriptionPasswordStatus;
    /**
     * 开方密码
     */
    private String prescriptionPassword;
    /**
     * 是否禁用
     */
    private Boolean disable;

    /**
     * 医生拓展字段
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private InquiryDoctorExtDto ext;

}