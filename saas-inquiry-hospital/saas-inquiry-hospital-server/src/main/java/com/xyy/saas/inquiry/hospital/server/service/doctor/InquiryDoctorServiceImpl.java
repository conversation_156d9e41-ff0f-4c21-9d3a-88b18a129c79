package com.xyy.saas.inquiry.hospital.server.service.doctor;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_HAS_ROLE_NO_ALLOW_ERROR;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.DOCTOR_IS_ONLINE_DELETE_ERROR;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.DOCTOR_IS_ONLINE_LOGOFF_ERROR;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.DOCTOR_IS_RECEPTING_LOGOFF_ERROR;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DOCTOR_CAN_NOT_AUDIT;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DOCTOR_CA_NOT_SET_AUTO;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DOCTOR_CREATE_EXISTS;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DOCTOR_NOT_EXISTS;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DOCTOR_ONLINE_STATUS_ERROR;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.yudao.module.system.api.permission.PermissionApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xyy.saas.inquiry.enums.doctor.AuditStatusEnum;
import com.xyy.saas.inquiry.enums.doctor.AutoGrabStatusEnum;
import com.xyy.saas.inquiry.enums.doctor.DoctorInquiryTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.OnlineStatusEnum;
import com.xyy.saas.inquiry.enums.signature.SignatureBizTypeEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorCardInfoDto;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.hospital.server.constant.HandleTypeEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorExtDto;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.SupervisionDoctorInfoDto;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.transmission.DoctorExternalTransmissionRespDto;
import com.xyy.saas.inquiry.hospital.enums.DoctorFillingStatusEnum;
import com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants;
import com.xyy.saas.inquiry.hospital.server.config.transimission.InquiryDoctorFillShaanxiRegulatoryProperties;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorAuditedRecordSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorDetailRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDoctorSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.convert.doctor.InquiryDoctorConvert;
import com.xyy.saas.inquiry.hospital.server.convert.hospital.InquiryHospitalDepartmentRelationConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorAuditedRecordDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorBillingDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorFilingDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorPracticeDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorWorkRecordDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryHospitalDeptDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryProfessionIdentificationDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDepartmentRelationDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.DoctorAuditedRecordMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.DoctorBillingMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.DoctorFilingMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.DoctorPracticeMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.DoctorWorkRecordMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryDoctorMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryDoctorStatusMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryHospitalDeptDoctorMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryProfessionIdentificationMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital.InquiryHospitalDepartmentRelationMapper;
import com.xyy.saas.inquiry.hospital.server.dal.redis.doctor.DoctorRedisDao;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalDetpDoctorRelationService;
import com.xyy.saas.inquiry.mq.doctor.DoctorAutoInquirySwitchProducer;
import com.xyy.saas.inquiry.mq.doctor.DoctorAutoInquiryTimerWheelEvent;
import com.xyy.saas.inquiry.mq.doctor.dto.DoctorAutoInquiryTimerWheelDto;
import com.xyy.saas.inquiry.mq.doctor.dto.DoctorAutoInquiryTimerWheelMessage;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.signature.api.ca.InquirySignatureCaAuthApi;
import com.xyy.saas.inquiry.signature.api.signature.InquiryUserSignatureInformationApi;
import com.xyy.saas.transmitter.api.transmission.TransmissionApi;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionConfigReqDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;


/**
 * 医生信息 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class InquiryDoctorServiceImpl implements InquiryDoctorService {

    @Resource
    private InquiryDoctorMapper inquiryDoctorMapper;

    @Resource
    private DoctorPracticeMapper doctorPracticeMapper;

    @Resource
    private DoctorFilingMapper doctorFilingMapper;

    @Resource
    private DoctorBillingMapper doctorBillingMapper;

    @Resource
    private DoctorWorkRecordMapper doctorWorkRecordMapper;

    @Resource
    private InquiryProfessionIdentificationMapper identificationMapper;

    @Resource
    private InquiryHospitalDetpDoctorRelationService doctorInquiryHospitalDetpDoctorRelationService;

    @Resource
    private InquiryHospitalDepartmentRelationMapper inquiryHospitalDepartmentRelationMapper;

    @Resource
    private InquiryHospitalDeptDoctorMapper inquiryHospitalDeptDoctorMapper;

    @Resource
    private DoctorAuditedRecordMapper doctorAuditedRecordMapper;

    @Resource
    private InquiryDoctorStatusMapper doctorStatusMapper;

    @Resource
    private InquiryProfessionIdentificationService identificationService;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private PermissionApi permissionApi;

    @DubboReference
    private InquiryUserSignatureInformationApi inquiryUserSignatureInformationApi;

    @Resource
    private DoctorQuickReplyMsgService doctorQuickReplyMsgService;

    @Resource
    private InquiryDoctorStatusService inquiryDoctorStatusService;

    @Resource
    private DoctorAutoInquirySwitchProducer doctorAutoInquirySwitchProducer;

    @DubboReference
    private InquiryApi inquiryApi;

    @Resource
    private TenantApi tenantApi;

    @Resource
    private DoctorRedisDao doctorRedisDao;

    @DubboReference
    private InquirySignatureCaAuthApi inquirySignatureCaAuthApi;

    @DubboReference(retries = 0)
    private TransmissionApi transmissionApi;


    @Resource
    private InquiryDoctorFillShaanxiRegulatoryProperties doctorFillShaanxiRegulatoryProperties;


    public InquiryDoctorServiceImpl getSelf() {
        return SpringUtil.getBean(InquiryDoctorServiceImpl.class);
    }

    /**
     * 创建医生信息
     *
     * @param createReqVO 创建信息
     * @return 创建医生的id
     */
    @Override
    public InquiryDoctorDO createInquiryDoctor(InquiryDoctorSaveReqVO createReqVO) {
        // 校验逻辑
        validateInput(createReqVO);
        // 设置用户ID
        setUserId(createReqVO);
        InquiryDoctorDO inquiryDoctor = getSelf().createDoctor(createReqVO);
        // 返回医生ID
        return inquiryDoctor;
    }

    @Transactional(rollbackFor = Exception.class)
    public InquiryDoctorDO createDoctor(InquiryDoctorSaveReqVO createReqVO) {
        // 获取医生基础信息并插入数据库
        InquiryDoctorDO inquiryDoctor = convertToInquiryDoctorDO(createReqVO);
        // 分配系统医生角色
        assignDoctorRole(createReqVO.getUserId());
        // 新增医生执业信息
        saveOrUpdateDoctorPractice(createReqVO,HandleTypeEnum.ADD);
        // 备案信息
        insertDoctorFiling(createReqVO);
        // 收款信息
        insertDoctorBilling(createReqVO);
        // 工作经历
        insertDoctorWorkRecords(createReqVO, inquiryDoctor);
        // 证件信息
        insertIdentificationInfo(createReqVO);
        // 初始化快捷语
        initDoctorQuickReplyMsg(inquiryDoctor.getId());
        // 保存医生关联医院信息
        saveDoctorHospitalInfo(createReqVO, inquiryDoctor);
        return inquiryDoctor;
    }

    /**
     * 初始化快捷语
     *
     * @param id 医生ID
     */
    private void initDoctorQuickReplyMsg(Long id) {
        doctorQuickReplyMsgService.initDoctorQuickReplyMsg(id);
    }

    /**
     * 新增医生证件信息
     *
     * @param createReqVO 创建信息
     */
    private void insertIdentificationInfo(InquiryDoctorSaveReqVO createReqVO) {
        List<InquiryProfessionIdentificationDO> identificationDOS = identificationService.getListByDoctorCreate(createReqVO);
        if (CollectionUtils.isEmpty(identificationDOS)) {
            return;
        }
        identificationMapper.insertBatch(identificationDOS);
    }

    /**
     * 新增医生收款信息
     *
     * @param createReqVO   创建信息
     * @param inquiryDoctor doctor信息
     */
    private void insertDoctorWorkRecords(InquiryDoctorSaveReqVO createReqVO, InquiryDoctorDO inquiryDoctor) {
        if (CollectionUtils.isEmpty(createReqVO.getJobItems())) {
            return;
        }
        List<DoctorWorkRecordDO> recordDOList = InquiryDoctorConvert.INSTANCE.convertWorkRecords(createReqVO.getJobItems());
        recordDOList.forEach(recordDO -> recordDO.setDoctorId(inquiryDoctor.getId()));
        doctorWorkRecordMapper.insertBatch(recordDOList);
    }

    /**
     * 新增医生收款信息
     *
     * @param createReqVO 创建信息
     */
    private void insertDoctorBilling(InquiryDoctorSaveReqVO createReqVO) {
        doctorBillingMapper.insert(InquiryDoctorConvert.INSTANCE.convertBilling(createReqVO));
    }

    /**
     * 添加医生备案信息
     *
     * @param createReqVO 创建信息
     */
    private void insertDoctorFiling(InquiryDoctorSaveReqVO createReqVO) {
        doctorFilingMapper.insert(InquiryDoctorConvert.INSTANCE.convertFiling(createReqVO));
    }

    /**
     * 创建医生信息校验
     *
     * @param createReqVO 创建信息
     */
    private void validateInput(InquiryDoctorSaveReqVO createReqVO) {
        // CA认证校验逻辑
        if (CollUtil.isNotEmpty(createReqVO.getHospitalDoctorItems()) && createReqVO.getHospitalDoctorItems().stream().anyMatch(d -> CollUtil.isNotEmpty(d.getAutoInquiryWayType()))) {
            throw exception(INQUIRY_DOCTOR_CA_NOT_SET_AUTO);
        }
    }

    /**
     * 保存医生基础信息
     *
     * @param createReqVO 创建信息
     * @return doctorId
     */
    private InquiryDoctorDO convertToInquiryDoctorDO(InquiryDoctorSaveReqVO createReqVO) {
        InquiryDoctorDO inquiryDoctor = InquiryDoctorConvert.INSTANCE.initConvertVO2DO(createReqVO, null);
        inquiryDoctorMapper.insert(inquiryDoctor);
        createReqVO.setDoctorId(inquiryDoctor.getId());
        return inquiryDoctor;
    }

    /**
     * 分配系统药师角色
     *
     * @param userId 用户ID
     */
    private void assignDoctorRole(long userId) {
        permissionApi.assignUserRoleWithSystemRoleCode(userId, RoleCodeEnum.DOCTOR.getCode());
    }

    /**
     * 保存医生关联医院信息
     *
     * @param createReqVO   创建信息
     * @param inquiryDoctor doctor信息
     */
    private void saveDoctorHospitalInfo(InquiryDoctorSaveReqVO createReqVO, InquiryDoctorDO inquiryDoctor) {
        if (CollectionUtils.isEmpty(createReqVO.getHospitalDoctorItems())) {
            return;
        }
        List<InquiryHospitalDeptDoctorDO> deptDoctorDOS = new ArrayList<>();
        createReqVO.getHospitalDoctorItems().forEach(item -> {
            InquiryHospitalDepartmentRelationDO relationDO = inquiryHospitalDepartmentRelationMapper.selectById(item.getHospitalDeptRelationId());
            deptDoctorDOS.addAll(InquiryHospitalDepartmentRelationConvert.INSTANCE.convertTODeptDoctorDOList(relationDO, inquiryDoctor, item));
        });
        inquiryHospitalDeptDoctorMapper.insertBatch(deptDoctorDOS);
        // 医生出诊状态下，需要写入redis ，未出诊直接返回
        if (ObjectUtil.notEqual(inquiryDoctor.getOnlineStatus(), OnlineStatusEnum.ONLINE.getCode())) {
            return;
        }
        // 更新医生已派单列表，根据当前接诊权限重新分配
        inquiryDoctorStatusService.updateDoctorCanReceiptInquiryList(inquiryDoctor);
        // 重新出诊，将医生推入待派单队列
        doctorRedisDao.doctorStartReception(inquiryDoctor, DoctorInquiryTypeEnum.MANUAL_INQUIRY.getCode(), null);
    }

    /**
     * 新增医生执业信息
     *
     * @param reqVo 创建信息
     */
    private void saveOrUpdateDoctorPractice(InquiryDoctorSaveReqVO reqVo , HandleTypeEnum handleTypeEnum) {
        DoctorPracticeDO practiceDO = InquiryDoctorConvert.INSTANCE.convertPractice(reqVo);
        InquiryDoctorDO doctorDO = inquiryDoctorMapper.selectById(practiceDO.getDoctorId());
        if(ObjectUtil.equals(handleTypeEnum, HandleTypeEnum.UPDATE)){
            DoctorPracticeDO bygone = doctorPracticeMapper.selectOne(DoctorPracticeDO::getDoctorId, reqVo.getDoctorId());
            if(ObjectUtil.isNotEmpty(bygone) && StringUtils.isNotBlank(bygone.getDoctorMedicareNo())){
                // 先从redis 缓存移除当前医生
                doctorRedisDao.removeDoctorFromMedicareQueue(doctorDO.getPref());
            }
            doctorPracticeMapper.updateByDoctorId(practiceDO);
        }
        if(ObjectUtil.equals(handleTypeEnum, HandleTypeEnum.ADD)){
            doctorPracticeMapper.insert(practiceDO);
        }
        // 医保编码为空直接返回
        if(StringUtils.isBlank(practiceDO.getDoctorMedicareNo())){
            return;
        }
        // 更新redis 缓存
        doctorRedisDao.pushDoctorFromMedicareQueue(reqVo.getEnvTag() , doctorDO.getPref());
    }

    /**
     * 创建医生时设置user信息
     *
     * @param createReqVO 创建信息
     */
    private void setUserId(InquiryDoctorSaveReqVO createReqVO) {
        Long userId = adminUserApi.createUser(InquiryDoctorConvert.INSTANCE.convertUser(createReqVO));
        createReqVO.setUserId(userId);
        InquiryDoctorDO doctorDO = inquiryDoctorMapper.selectOne(InquiryDoctorDO::getUserId, userId);
        if (doctorDO != null) {
            throw exception(INQUIRY_DOCTOR_CREATE_EXISTS);
        }

        // 医生创建判断药师角色
        if (CollUtil.isNotEmpty(permissionApi.selectUserRoleByUserIdRoleCodes(userId, Set.of(RoleCodeEnum.PHARMACIST.getCode())))) {
            throw exception(USER_HAS_ROLE_NO_ALLOW_ERROR, RoleCodeEnum.PHARMACIST.getCode());
        }
    }

    /**
     * 更新医生信息
     *
     * @param updateReqVO 更新信息
     */
    @Override
    public void updateInquiryDoctor(InquiryDoctorSaveReqVO updateReqVO) {
        InquiryDoctorDO inquiryDoctor = inquiryDoctorMapper.selectById(updateReqVO.getId());
        Assert.notNull(inquiryDoctor, INQUIRY_DOCTOR_NOT_EXISTS.getMsg());
        // 自动开方校验CA认证
        checkDoctorAutoInquiryCa(updateReqVO.getHospitalDoctorItems(), inquiryDoctor.getUserId());
        List<DoctorAutoInquiryTimerWheelDto> wheels = doctorInquiryHospitalDetpDoctorRelationService.queryDoctorAutoInquiryTimeWheelDto(Collections.singletonList(inquiryDoctor.getPref()));
        // 事务更新医生
        InquiryDoctorDO doctorDO = getSelf().updateDoctor(updateReqVO);
        // 发送自动开方医师出停诊MQ
        doctorAutoInquirySwitchProducer.sendMessage(DoctorAutoInquiryTimerWheelEvent.builder().msg(DoctorAutoInquiryTimerWheelMessage.builder().doctorPref(doctorDO.getPref()).originWheels(wheels).build()).build());

    }

    /**
     * 自动开方校验CA认证
     *
     * @param hospitalDoctorItems
     * @param userId
     */
    public void checkDoctorAutoInquiryCa(List<InquiryHospitalDeptDoctorSaveReqVO> hospitalDoctorItems, Long userId) {
        if (CollUtil.isNotEmpty(hospitalDoctorItems) && hospitalDoctorItems.stream().anyMatch(d -> CollUtil.isNotEmpty(d.getAutoInquiryWayType()))) {
            if (!inquirySignatureCaAuthApi.isCaAuthFreeSign(userId, SignaturePlatformEnum.FDD)) {
                throw exception(INQUIRY_DOCTOR_CA_NOT_SET_AUTO);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public InquiryDoctorDO updateDoctor(InquiryDoctorSaveReqVO updateReqVO) {
        // 检查医生状态
        checkDoctorOnlineStatus(updateReqVO);
        InquiryDoctorDO inquiryDoctor = inquiryDoctorMapper.selectById(updateReqVO.getId());
        Assert.notNull(inquiryDoctor, INQUIRY_DOCTOR_NOT_EXISTS.getMsg());
        updateReqVO.setDoctorId(updateReqVO.getId());
        // 获取基础信息
        InquiryDoctorDO updateObj = InquiryDoctorConvert.INSTANCE.initConvertVO2DO(updateReqVO, inquiryDoctor);
        // 当前医生非审核通过情况下，编辑信息之后将状态置为待审核
        if (!inquiryDoctor.getAuditStatus().equals(AuditStatusEnum.APPROVED.getCode())) {
            updateObj.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        }
        // 更新执业信息
        saveOrUpdateDoctorPractice(updateReqVO,HandleTypeEnum.UPDATE);
        // 更新备案信息
        updateDoctorFiling(updateReqVO, inquiryDoctor);
        // 更新收款信息
        doctorBillingMapper.updateByDoctorId(InquiryDoctorConvert.INSTANCE.convertBilling(updateReqVO));
        // 更新工作记录，先删除旧数据
        doctorWorkRecordMapper.deleteByDoctorId(inquiryDoctor.getId());
        // 插入工作记录
        insertDoctorWorkRecords(updateReqVO, inquiryDoctor);
        // 更新证件信息,先删除旧数据
        identificationMapper.deleteByPersonId(inquiryDoctor.getId());
        // 插入证件信息
        insertIdentificationInfo(updateReqVO);
        // 关联医院信息，先删除旧数据
        deleteDoctorHospitalInfo(inquiryDoctor);
        // 更新基础信息
        inquiryDoctorMapper.updateById(updateObj);
        // 插入关联医院信息
        saveDoctorHospitalInfo(updateReqVO, updateObj);
        return inquiryDoctorMapper.selectById(updateObj.getId());
    }

    private void checkDoctorOnlineStatus(InquiryDoctorSaveReqVO updateReqVO) {
        // 查询医生信息
        InquiryDoctorDO doctorDO = inquiryDoctorMapper.selectById(updateReqVO.getId());
        // 停诊状态直接返回
        if (ObjectUtil.equals(OnlineStatusEnum.OFFLINE.getCode(), doctorDO.getOnlineStatus())) {
            return;
        }
        // 医生已出诊状态下，修改时至少保留一个真人出诊的接诊信息
        if (CollectionUtils.isEmpty(updateReqVO.getHospitalDoctorItems())) {
            throw exception(INQUIRY_DOCTOR_ONLINE_STATUS_ERROR);
        }
        // 所有关联医院信息，至少保留一个真人出诊的接诊信息
        if (updateReqVO.getHospitalDoctorItems().stream().allMatch(d -> CollectionUtils.isEmpty(d.getInquiryWayType()))) {
            throw exception(INQUIRY_DOCTOR_ONLINE_STATUS_ERROR);
        }
    }

    private void updateDoctorFiling(InquiryDoctorSaveReqVO updateReqVO, InquiryDoctorDO inquiryDoctor) {
        DoctorFilingDO updateObj = InquiryDoctorConvert.INSTANCE.convertFiling(updateReqVO);
        // 更新失败 或者 省份为空， 直接return
        if (doctorFilingMapper.updateByDoctorId(updateObj) <= 0 || StringUtils.isBlank(updateObj.getOrgProvinceCode())) {
            return;
        }
        // 更新成功，并且当前医生处于出诊状态，需要更新redis
        if (ObjectUtil.equals(OnlineStatusEnum.OFFLINE.getCode(), inquiryDoctor.getOnlineStatus())) {
            return;
        }
        // 设置医生省份缓存
        doctorRedisDao.setDoctorProvinceCode(inquiryDoctor.getPref(), updateObj.getOrgProvinceCode());
    }

    /**
     * 删除关联医院信息
     *
     * @param inquiryDoctor 医生信息
     */
    private void deleteDoctorHospitalInfo(InquiryDoctorDO inquiryDoctor) {
        // 医生处于出诊状态，需要更新redis
        if (ObjectUtil.equals(inquiryDoctor.getOnlineStatus(), OnlineStatusEnum.ONLINE.getCode())) {
            // 先清除redis(inquiryType 为空表示移除  手动  所有开方池)
            doctorRedisDao.doctorStopReception(inquiryDoctor, DoctorInquiryTypeEnum.MANUAL_INQUIRY.getCode(), null);
        }
        // 批量删除当前出诊信息
        inquiryHospitalDeptDoctorMapper.deleteByDoctorPref(inquiryDoctor.getPref());
    }

    /**
     * 删除医生信息
     *
     * @param id 编号
     */
    @Override
    public void deleteInquiryDoctor(Long id) {
        InquiryDoctorDO inquiryDoctor = inquiryDoctorMapper.selectById(id);
        // 校验存在
        Assert.notNull(inquiryDoctor, INQUIRY_DOCTOR_NOT_EXISTS.getMsg());
        // 判断医生是否已出诊
        if (ObjectUtil.equals(inquiryDoctor.getOnlineStatus(), OnlineStatusEnum.ONLINE.getCode())) {
            throw exception(DOCTOR_IS_ONLINE_DELETE_ERROR);
        }
        // 发送自动开方医师出停诊MQ
        List<DoctorAutoInquiryTimerWheelDto> wheels = doctorInquiryHospitalDetpDoctorRelationService.queryDoctorAutoInquiryTimeWheelDto(Collections.singletonList(inquiryDoctor.getPref()));
        getSelf().deleteDoctor(inquiryDoctor);
        doctorAutoInquirySwitchProducer.sendMessage(DoctorAutoInquiryTimerWheelEvent.builder().msg(DoctorAutoInquiryTimerWheelMessage.builder().doctorPref(inquiryDoctor.getPref()).originWheels(wheels).build()).build());
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteDoctor(InquiryDoctorDO inquiryDoctor) {
        // 删除用户
        adminUserApi.deleteUserSystem(inquiryDoctor.getUserId());
        // 删除基础信息
        inquiryDoctorMapper.deleteById(inquiryDoctor.getId());
        // 删除执业信息
        doctorPracticeMapper.delete(DoctorPracticeDO::getDoctorId, inquiryDoctor.getId());
        // 删除备案信息
        doctorFilingMapper.delete(DoctorFilingDO::getDoctorId, inquiryDoctor.getId());
        // 删除收款信息
        doctorBillingMapper.delete(DoctorBillingDO::getDoctorId, inquiryDoctor.getId());
        // 删除工作记录
        doctorWorkRecordMapper.delete(DoctorWorkRecordDO::getDoctorId, inquiryDoctor.getId());
        // 删除证件信息
        identificationMapper.delete(InquiryProfessionIdentificationDO::getPersonId, inquiryDoctor.getId());
        // 删除关联医院信息
        inquiryHospitalDeptDoctorMapper.delete(InquiryHospitalDeptDoctorDO::getDoctorPref, inquiryDoctor.getPref());
    }

    /**
     * 获取医生详情
     *
     * @param id 编号
     * @return 医生详情
     */
    @Override
    public InquiryDoctorDetailRespVO getInquiryDoctor(Long id) {
        InquiryDoctorDetailRespVO result = new InquiryDoctorDetailRespVO();
        InquiryDoctorDO inquiryDoctor = inquiryDoctorMapper.selectById(id);
        if (inquiryDoctor == null) {
            throw exception(INQUIRY_DOCTOR_NOT_EXISTS);
        }
        // 填充基本信息
        InquiryDoctorConvert.INSTANCE.convertToVo(inquiryDoctor, result);
        // 查询执业信息
        Optional.ofNullable(doctorPracticeMapper.selectOne(DoctorPracticeDO::getDoctorId, inquiryDoctor.getId())).ifPresent(doctorPracticeDO -> {
            InquiryDoctorConvert.INSTANCE.convertPracticeWithEvaluate(doctorPracticeDO, result);
        });
        // 查询备案信息
        Optional.ofNullable(doctorFilingMapper.selectOne(DoctorFilingDO::getDoctorId, inquiryDoctor.getId())).ifPresent(doctorFilingDO -> InquiryDoctorConvert.INSTANCE.convertFilingToVo(doctorFilingDO, result));
        // 收款信息
        Optional.ofNullable(doctorBillingMapper.selectOne(DoctorBillingDO::getDoctorId, inquiryDoctor.getId())).ifPresent(doctorBillingDO -> InquiryDoctorConvert.INSTANCE.convertBillingToVo(doctorBillingDO, result));
        // 工作记录
        Optional.ofNullable(doctorWorkRecordMapper.selectList(DoctorWorkRecordDO::getDoctorId, inquiryDoctor.getId()))
            .ifPresent(doctorWorkRecordDOList -> result.setJobItems(InquiryDoctorConvert.INSTANCE.convertWorkRecordsToVo(doctorWorkRecordDOList)));
        // 证件信息
        identificationService.setDoctorIdentification(result);
        // 关联医院
        Optional.ofNullable(inquiryHospitalDeptDoctorMapper.selectList(InquiryHospitalDeptDoctorDO::getDoctorPref, inquiryDoctor.getPref()))
            .ifPresent(hospitalDoctorDOList -> result.setHospitalDoctorItems(InquiryHospitalDepartmentRelationConvert.INSTANCE.convertToConfigRespVOList(hospitalDoctorDOList)));
        // 审核记录
        Optional.ofNullable(doctorAuditedRecordMapper.selectList(DoctorAuditedRecordDO::getDoctorId, inquiryDoctor.getId())).ifPresent(
            doctorAuditedRecordDOS -> result.setAuditedRecords(InquiryDoctorConvert.INSTANCE.convertAuditedRecordsToVo(doctorAuditedRecordDOS.stream().sorted(Comparator.comparing(DoctorAuditedRecordDO::getAuditTime).reversed()).toList())));
        // 电子签名图片url
        result.setDoctorElectronSignImgUrl(inquiryUserSignatureInformationApi.getInquiryUserSignatureUrl(inquiryDoctor.getUserId(), SignaturePlatformEnum.FDD));
        // 电子签章图片url
        result.setDoctorElectronSignChapterUrl(inquiryUserSignatureInformationApi.getInquiryUserSignatureUrl(inquiryDoctor.getUserId(), SignaturePlatformEnum.FDD, SignatureBizTypeEnum.USER_ELE_SIGN));

        return result;
    }


    /**
     * 分页查询医生列表
     *
     * @param pageReqVO 分页查询
     * @return PageResult<InquiryDoctorDO>
     */
    @Override
    public PageResult<InquiryDoctorRespVO> getInquiryDoctorPage(InquiryDoctorPageReqVO pageReqVO) {
        // 存在医院的条件时需要先查医院下医生
        if (StringUtils.isNotBlank(pageReqVO.getHospitalPref())) {
            // 查询当前医院下所有医生
            List<String> doctorPrefs =
                inquiryHospitalDeptDoctorMapper.selectList(InquiryHospitalDepartmentRelationPageReqVO.builder().hospitalPref(pageReqVO.getHospitalPref()).build()).stream().map(InquiryHospitalDeptDoctorDO::getDoctorPref).distinct().toList();
            if (!CollectionUtils.isEmpty(doctorPrefs)) {
                pageReqVO.setDoctorPrefs(doctorPrefs);
            }
        }
        IPage<InquiryDoctorRespVO> page = inquiryDoctorMapper.selectDoctorPage(MyBatisUtils.buildPage(pageReqVO), pageReqVO);
        page.getRecords().forEach(InquiryDoctorRespVO::flattenExt);
        // 填充用户账号状态
        fillUserAccountStatus(page.getRecords());
        return new PageResult(page.getRecords(), page.getTotal());
    }

    private void fillUserAccountStatus(List<InquiryDoctorRespVO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        // 查询用户
        List<AdminUserRespDTO> userDtos = adminUserApi.getUserList(records.stream().map(InquiryDoctorRespVO::getUserId).distinct().toList());
        if (CollectionUtils.isEmpty(userDtos)) {
            return;
        }
        Map<Long, Integer> userStatusMap = userDtos.stream().collect(Collectors.toMap(AdminUserRespDTO::getId, AdminUserRespDTO::getStatus, (k1, k2) -> k2));
        records.forEach(vo -> {
            vo.setUserAccountStatus(userStatusMap.get(vo.getUserId()));
        });
    }

    /**
     * 查询医生列表
     *
     * @param reqVo 查询条件
     * @return 医生信息不分页
     */
    @Override
    public List<InquiryDoctorDO> getInquiryDoctorList(InquiryDoctorPageReqVO reqVo) {
        return inquiryDoctorMapper.selectList(reqVo);
    }

    /**
     * 医生审核
     */
    @Override
    public Integer auditInquiryDoctor(DoctorAuditedRecordSaveReqVO auditReqVO) {
        InquiryDoctorDO doctorDO = inquiryDoctorMapper.selectOne(InquiryDoctorDO::getId, auditReqVO.getDoctorId());
        // 校验存在
        Assert.notNull(doctorDO, INQUIRY_DOCTOR_NOT_EXISTS.getMsg());
        if (doctorDO.getAuditStatus() != AuditStatusEnum.PENDING.getCode()) {
            throw exception(INQUIRY_DOCTOR_CAN_NOT_AUDIT);
        }
        int i = getSelf().saveAuditInquiryDoctor(auditReqVO, doctorDO);
        // 审核通过发送医生自动开方出停诊mq
        if (Objects.equals(AuditStatusEnum.APPROVED.getCode(), doctorDO.getAuditStatus())) {
            doctorAutoInquirySwitchProducer.sendMessage(DoctorAutoInquiryTimerWheelEvent.builder().msg(DoctorAutoInquiryTimerWheelMessage.builder().doctorPref(doctorDO.getPref()).build()).build());
        }
        return i;
    }

    @Transactional(rollbackFor = Exception.class)
    public int saveAuditInquiryDoctor(DoctorAuditedRecordSaveReqVO auditReqVO, InquiryDoctorDO doctorDO) {
        DoctorAuditedRecordDO auditedRecordDO = InquiryDoctorConvert.INSTANCE.convertAuditRecordVoToDo(auditReqVO);
        doctorAuditedRecordMapper.insert(auditedRecordDO);
        doctorDO.setAuditStatus(auditedRecordDO.getAuditResult());
        int i = inquiryDoctorMapper.updateById(doctorDO);
        return i;
    }

    /**
     * 根据医生guid 获取医生基础信息
     *
     * @param doctorId doctorId
     * @return doctorDO
     */
    @Override
    public InquiryDoctorDO getInquiryDoctorByDoctorId(Long doctorId) {
        InquiryDoctorDO doctorDO = inquiryDoctorMapper.selectOne(InquiryDoctorDO::getId, doctorId);
        if (ObjectUtils.isEmpty(doctorDO)) {
            throw exception(INQUIRY_DOCTOR_NOT_EXISTS);
        }
        return doctorDO;
    }

    /**
     * 根据医生userId 获取医生基础信息
     *
     * @param userId userId
     * @return doctorDO
     */
    @Override
    public InquiryDoctorDO getInquiryDoctorByUserId(Long userId) {
        InquiryDoctorDO doctorDO = inquiryDoctorMapper.selectOne(InquiryDoctorDO::getUserId, userId);
        if (ObjectUtils.isEmpty(doctorDO)) {
            throw exception(INQUIRY_DOCTOR_NOT_EXISTS);
        }
        return doctorDO;
    }

    @Override
    public InquiryDoctorDO getDoctorByUserId(Long userId) {
        return inquiryDoctorMapper.selectOne(InquiryDoctorDO::getUserId, userId);
    }

    /**
     * 根据医生编码查询医生信息
     *
     * @param doctorPref doctorPref
     * @return doctorDO
     */
    @Override
    public InquiryDoctorDO getRequireInquiryDoctorByDoctorPref(String doctorPref) {
        InquiryDoctorDO doctorDO = inquiryDoctorMapper.selectOne(InquiryDoctorDO::getPref, doctorPref);
        if (ObjectUtils.isEmpty(doctorDO)) {
            throw exception(INQUIRY_DOCTOR_NOT_EXISTS);
        }
        return doctorDO;
    }

    @Override
    public InquiryDoctorDO getInquiryDoctorByDoctorPref(String doctorPref) {
        return inquiryDoctorMapper.selectOne(InquiryDoctorDO::getPref, doctorPref);
    }

    @Override
    public void updateDoctorOnlineStatus(Long id, OnlineStatusEnum onlineStatusEnum, LocalDateTime startTime, LocalDateTime endTime) {
        InquiryDoctorDO doctor = getInquiryDoctorByDoctorId(id);
        inquiryDoctorMapper.updateById(InquiryDoctorDO.builder().id(doctor.getId()).onlineStatus(onlineStatusEnum.getCode()).startInquiryTime(startTime).endInquiryTime(endTime).build());

    }

    /**
     * 超时取消医生接诊
     *
     * @param inquiryPref 问诊编码
     */
    @Override
    public void inquiryTimeOutCancelForDoctorReception(String inquiryPref) {
        InquiryRecordDto inquiryRecordDto = inquiryApi.getInquiryRecord(inquiryPref);
        TenantDto tenantDto = tenantApi.getTenant(inquiryRecordDto.getTenantId());
        doctorRedisDao.doctorOverInquiry(inquiryRecordDto, tenantDto, this.getRequireInquiryDoctorByDoctorPref(inquiryRecordDto.getDoctorPref()));
    }

    /**
     * 根据医生编码获取医生卡片信息
     *
     * @param doctorPref 医生编码
     * @return 医生卡片信息
     */
    @Override
    public InquiryDoctorCardInfoDto getDoctorCardInfoByDoctorPref(String doctorPref) {
        // 查询医生基础信息
        InquiryDoctorDO doctorDO = getInquiryDoctorByDoctorPref(doctorPref);
        if (doctorDO == null) {
            return new InquiryDoctorCardInfoDto();
        }
        // 查询医生执业信息
        DoctorPracticeDO practiceDO = doctorPracticeMapper.selectOne(DoctorPracticeDO::getDoctorId, doctorDO.getId());
        // 医生卡片信息
        return InquiryDoctorConvert.INSTANCE.convertToCardInfo(doctorDO, practiceDO);
    }


    @Override
    public InquiryDoctorDetailRespVO getInquiryDoctorByMobile(String mobile) {
        AdminUserRespDTO user = adminUserApi.getUserByMobile(mobile);
        if (user != null) {
            InquiryDoctorDO doctorDO = inquiryDoctorMapper.selectOne(InquiryDoctorDO::getUserId, user.getId());
            if (doctorDO != null) {
                return getInquiryDoctor(doctorDO.getId());
            }
        }
        return null;
    }

    /**
     * 医生注销检查
     *
     * @return true/false
     */
    @Override
    public Boolean logOffCheck() {
        InquiryDoctorDO doctorDO = getInquiryDoctorByUserId(getLoginUserId());
        if (ObjectUtils.isEmpty(doctorDO)) {
            throw exception(INQUIRY_DOCTOR_NOT_EXISTS);
        }
        // 是否存在接诊中的问诊单
        List<String> receptionList = doctorRedisDao.getDoctorReceptionList(doctorDO.getPref(), doctorDO.getEnvTag());
        if (!CollectionUtils.isEmpty(receptionList)) {
            throw exception(DOCTOR_IS_RECEPTING_LOGOFF_ERROR);
        }
        // 查询当前医生是否已出诊
        if (ObjectUtil.equals(doctorDO.getOnlineStatus(), OnlineStatusEnum.ONLINE.getCode())) {
            throw exception(DOCTOR_IS_ONLINE_LOGOFF_ERROR);
        }
        return Boolean.TRUE;
    }

    /**
     * 关闭当前医生自动抢单功能
     *
     * @param doctor
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeDoctorAutoGrab(InquiryDoctorDO doctor) {
        // 检查医生是否存在
        if (ObjectUtils.isEmpty(doctor)) {
            throw exception(INQUIRY_DOCTOR_NOT_EXISTS);
        }
        // 更新医生自动抢单状态为关闭
        inquiryDoctorMapper.updateById(InquiryDoctorDO.builder()
                .id(doctor.getId())
                .autoGrabStatus(AutoGrabStatusEnum.CLOSE.getCode())
                .build());
        // 从自动抢单队列中移除医生
        doctorRedisDao.removeDoctorFromAutoGrabQueue(doctor);
    }


    /**
     * 陕西监管备案
     *
     * @param doctorId
     * @return
     */
    public DoctorExternalTransmissionRespDto fillShaanxiRegulatory(Long doctorId) {
        // 备案结果提示信息
        List<String> msgList = new ArrayList<>();

        // 1. 根据doctorIds查询医生信息
        List<InquiryDoctorDO> doctors = inquiryDoctorMapper.selectBatchIds(List.of(doctorId));
        if (CollUtil.isEmpty(doctors)) {
            throw exception(ErrorCodeConstants.INQUIRY_DOCTOR_NOT_EXISTS);
        }

        // 医师诊疗业务备案
        List<DoctorExternalTransmissionRespDto> respDtoList = physicianDiagnosisTreatmentRecord2(doctors);
        boolean apiSuccess1 = doctorFillShaanxiRegulatoryProperties.isPhysicianDiagnosisTreatmentRecordApiSuccess(respDtoList);
        if (!CollectionUtils.isEmpty(respDtoList) && respDtoList.getFirst() != null && StringUtils.isNotBlank(respDtoList.getFirst().getMsg())) {
            msgList.add("%s %s：%s".formatted(
                apiSuccess1 ? "✅" : "❌",
                doctorFillShaanxiRegulatoryProperties.getPhysicianDiagnosisTreatmentRecordApiName(),
                respDtoList.getFirst().getMsg()));
        }

        // 医疗人员信息上报
        List<DoctorExternalTransmissionRespDto> respDtoList2 = medicalPersonnelInformationReport2(doctors);
        boolean apiSuccess2 = doctorFillShaanxiRegulatoryProperties.isMedicalPersonnelInformationReportApiSuccess(respDtoList2);
        if (!CollectionUtils.isEmpty(respDtoList2) && respDtoList2.getFirst() != null && StringUtils.isNotBlank(respDtoList2.getFirst().getMsg())) {
            msgList.add("%s %s：%s".formatted(
                apiSuccess2 ? "✅" : "❌",
                doctorFillShaanxiRegulatoryProperties.getMedicalPersonnelInformationReportApiName(),
                respDtoList2.getFirst().getMsg()));
        }

        // 接口调用全部成功
        if (!(apiSuccess1 && apiSuccess2)) {
            throw exception(ErrorCodeConstants.DOCTOR_FILL_SHAANXI_REGULATORY_FAIL, String.join("\n", msgList));
        }

        // 修改医生备案状态
        InquiryDoctorDO upd = new InquiryDoctorDO();
        upd.setId(doctorId);
        upd.setExt(Optional.ofNullable(doctors.getFirst().getExt())
            .orElse(new InquiryDoctorExtDto())
            .setFillingStatus4ShaanxiRegulatory(DoctorFillingStatusEnum.DONE.getType()));
        inquiryDoctorMapper.updateById(upd);

        return new DoctorExternalTransmissionRespDto()
            .setDoctorPrefList(List.of(doctors.getFirst().getPref()))
            .setMsg(String.join("\n", msgList));
    }

    @Override
    public List<DoctorExternalTransmissionRespDto> physicianDiagnosisTreatmentRecord(List<Long> doctorIds) {
        // 1. 根据doctorIds查询医生信息
        List<InquiryDoctorDO> doctors = inquiryDoctorMapper.selectBatchIds(doctorIds);

        return physicianDiagnosisTreatmentRecord2(doctors);
    }
    public List<DoctorExternalTransmissionRespDto> physicianDiagnosisTreatmentRecord2(List<InquiryDoctorDO> doctors) {
        return supervisionDoctorReport(doctors, NodeTypeEnum.INTERNET_SUPERVISION_PHYSICIAN_DIAGNOSIS_TREATMENT_RECORD, false);
    }

    @Override
    public List<DoctorExternalTransmissionRespDto> medicalPersonnelInformationReport(List<Long> doctorIds) {
        // 1. 根据doctorIds查询医生信息
        List<InquiryDoctorDO> doctors = inquiryDoctorMapper.selectBatchIds(doctorIds);

        return medicalPersonnelInformationReport2(doctors);
    }
    public List<DoctorExternalTransmissionRespDto> medicalPersonnelInformationReport2(List<InquiryDoctorDO> doctors) {
        return supervisionDoctorReport(doctors, NodeTypeEnum.INTERNET_SUPERVISION_MEDICAL_PERSONNEL_INFORMATION_REPORT, true);
    }

    /**
     * 互联网监管-医师诊疗业务备案、医疗人员信息上报
     *
     * @param doctors
     * @param nodeType
     * @return
     */
    private List<DoctorExternalTransmissionRespDto> supervisionDoctorReport(List<InquiryDoctorDO> doctors, NodeTypeEnum nodeType, boolean batch) {
        if (CollUtil.isEmpty(doctors)) {
            throw exception(ErrorCodeConstants.INQUIRY_DOCTOR_NOT_EXISTS);
        }
        List<Long> doctorIds = doctors.stream().map(InquiryDoctorDO::getId).toList();
        // 查询医生执业信息
        Map<Long, DoctorPracticeDO> doctorPracticeMap = doctorPracticeMapper.selectList(DoctorPracticeDO::getDoctorId, doctorIds)
            .stream().collect(Collectors.toMap(DoctorPracticeDO::getDoctorId, Function.identity(), (a, b) -> b));
        // 查询医生科室信息
        List<String> doctorPrefList = doctors.stream().map(InquiryDoctorDO::getPref).toList();
        // 取出医生在当前医院的科室信息
        Map<String, List<InquiryHospitalDeptDoctorDO>> doctorDeptMap = inquiryHospitalDeptDoctorMapper.selectList(InquiryHospitalDeptDoctorDO::getDoctorPref, doctorPrefList)
            .stream().collect(Collectors.groupingBy(InquiryHospitalDeptDoctorDO::getDoctorPref));

        List<SupervisionDoctorInfoDto> supervisionDoctorInfoDtoList = doctors.stream().map(dct -> {
            // 执业信息
            DoctorPracticeDO practiceDO = doctorPracticeMap.get(dct.getId());
            // 科室信息
            List<InquiryHospitalDeptDoctorDO> deptDoctorDOS = doctorDeptMap.get(dct.getPref());
            if (practiceDO == null || CollUtil.isEmpty(deptDoctorDOS)) {
                return null;
            }

            // 取第一个科室
            InquiryHospitalDeptDoctorDO deptDoctorDO = deptDoctorDOS.getFirst();
            return SupervisionDoctorInfoDto.builder()
                .doctorInfo(InquiryDoctorConvert.INSTANCE.convertToDto(dct))
                .doctorPracticeInfo(InquiryDoctorConvert.INSTANCE.convertDo2Dto(practiceDO))
                .doctorDeptInfo(InquiryDoctorConvert.INSTANCE.convertDoToDto(deptDoctorDO))
                .build();
        }).filter(Objects::nonNull).toList();

        if (CollUtil.isEmpty(supervisionDoctorInfoDtoList)) {
            throw exception(ErrorCodeConstants.DOCTOR_PRACTICE_NOT_EXISTS);
        }

        // 2. 组装TransmissionReqDTO
        TransmissionConfigReqDTO configReqDTO = TransmissionConfigReqDTO.builder()
            .nodeType(nodeType)
            .build();


        if (batch) {
            TransmissionReqDTO trd = TransmissionReqDTO.buildReq(configReqDTO, supervisionDoctorInfoDtoList);
            log.info("start-{} 节点: doctorIds:{}, trd:{}", nodeType.getDesc(), doctorIds, trd);
            CommonResult<DoctorExternalTransmissionRespDto> commonResult = transmissionApi.contractInvoke(trd, DoctorExternalTransmissionRespDto.class);
            log.info("  end-{} 节点: doctorIds:{}, commonResult:{}", nodeType.getDesc(), doctorIds, commonResult);
            return List.of(commonResult.getData());
        }

        return supervisionDoctorInfoDtoList.stream().map(dct -> {
            TransmissionReqDTO trd = TransmissionReqDTO.buildReq(configReqDTO, dct);
            log.info("start-{} 节点: doctorId:{}, trd:{}", nodeType.getDesc(), dct.getDoctorInfo().getPref(), trd);
            CommonResult<DoctorExternalTransmissionRespDto> commonResult = transmissionApi.contractInvoke(trd, DoctorExternalTransmissionRespDto.class);
            log.info("  end-{} 节点: doctorId:{}, commonResult:{}", nodeType.getDesc(), dct.getDoctorInfo().getPref(), commonResult);
            return commonResult.getData();
        }).toList();

    }


}