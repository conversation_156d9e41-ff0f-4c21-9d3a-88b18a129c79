package com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryHospitalDeptDoctorDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Set;

/**
 * 医院医生关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryHospitalDeptDoctorMapper extends BaseMapperX<InquiryHospitalDeptDoctorDO> {

    /**
     * 条件查询医院医生关系列表
     *
     * @param reqVO
     * @return
     */
    default List<InquiryHospitalDeptDoctorDO> selectList(InquiryHospitalDepartmentRelationPageReqVO reqVO) {
        final LambdaQueryWrapperX<InquiryHospitalDeptDoctorDO> queryWrapper = new LambdaQueryWrapperX<InquiryHospitalDeptDoctorDO>()
            .eqIfPresent(InquiryHospitalDeptDoctorDO::getHospitalDeptRelationId, reqVO.getHospitalDeptRelationId())
            .eqIfPresent(InquiryHospitalDeptDoctorDO::getHospitalPref, reqVO.getHospitalPref())
            .eqIfPresent(InquiryHospitalDeptDoctorDO::getDeptPref, reqVO.getDeptPref())
            .inIfPresent(InquiryHospitalDeptDoctorDO::getDoctorPref, reqVO.getDoctorPrefs())
            .eqIfPresent(InquiryHospitalDeptDoctorDO::getDoctorPref, reqVO.getDoctorPref())
            .eqIfPresent(InquiryHospitalDeptDoctorDO::getInquiryType, reqVO.getInquiryType())
            .eqIfPresent(InquiryHospitalDeptDoctorDO::getDoctorType, reqVO.getDoctorType())
            .inIfPresent(InquiryHospitalDeptDoctorDO::getInquiryWayType, reqVO.getInquiryWayTypes())
            .eq(InquiryHospitalDeptDoctorDO::getDisabled, 0);
        return selectList(queryWrapper);
    }


    void deleteByDoctorPref(String doctorPref);

    void deleteByIds(List<Long> ids);

    void deleteById(Long id);

    List<String> selectDoctorHospitalList(String doctorPref);

    List<InquiryHospitalDeptDoctorDO> getFullAutoInquiryDoctorList();

    /**
     * 根据医院编码、医生编码和科室编码删除关系记录 用于批量保存时的先删除操作
     *
     * @param hospitalPref 医院编码
     * @param doctorPref   医生编码
     * @param deptPrefs    科室编码集合
     */
    void deleteByHospitalDoctorDept(String doctorPref, String hospitalPref, Set<String> deptPrefs);
}