package com.xyy.saas.inquiry.hospital.server.dal.mysql.prescription.external;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.external.vo.SaasPrescriptionExternalPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.external.SaasPrescriptionExternalDO;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.Mapper;

/**
 * 外配(电子)处方记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SaasPrescriptionExternalMapper extends BaseMapperX<SaasPrescriptionExternalDO> {

    default SaasPrescriptionExternalDO selectOneByCondition(SaasPrescriptionExternalPageReqVO reqVO) {
        return selectOne(getQueryWrapper(reqVO), false);
    }

    default List<SaasPrescriptionExternalDO> selectListByCondition(SaasPrescriptionExternalPageReqVO reqVO) {
        return selectList(getQueryWrapper(reqVO));
    }

    default PageResult<SaasPrescriptionExternalDO> selectPage(SaasPrescriptionExternalPageReqVO reqVO) {
        return selectPage(reqVO, getQueryWrapper(reqVO));
    }

    private static LambdaQueryWrapperX<SaasPrescriptionExternalDO> getQueryWrapper(SaasPrescriptionExternalPageReqVO reqVO) {
        return new LambdaQueryWrapperX<SaasPrescriptionExternalDO>()
            .eqIfPresent(SaasPrescriptionExternalDO::getTenantId, Optional.ofNullable(reqVO.getTenantId()).orElseGet(TenantContextHolder::getRequiredTenantId))
            .eqIfPresent(SaasPrescriptionExternalDO::getPref, reqVO.getPref())
            .eqIfPresent(SaasPrescriptionExternalDO::getBizId, reqVO.getBizId())
            .eqIfPresent(SaasPrescriptionExternalDO::getBizType, reqVO.getBizType())
            .eqIfPresent(SaasPrescriptionExternalDO::getBizChannelType, reqVO.getBizChannelType())
            .eqIfPresent(SaasPrescriptionExternalDO::getMedicalVisitId, reqVO.getMedicalVisitId())
            .eqIfPresent(SaasPrescriptionExternalDO::getExternalType, reqVO.getExternalType())
            .eqIfPresent(SaasPrescriptionExternalDO::getPatientPref, reqVO.getPatientPref())
            .likeIfPresent(SaasPrescriptionExternalDO::getPatientName, reqVO.getPatientName())
            .eqIfPresent(SaasPrescriptionExternalDO::getExternalRxPref, reqVO.getExternalRxPref())
            .eqIfPresent(SaasPrescriptionExternalDO::getPatientIdCard, reqVO.getPatientIdCard())
            .eqIfPresent(SaasPrescriptionExternalDO::getFixMedicalInstitutionsCode, reqVO.getFixMedicalInstitutionsCode())
            .eqIfPresent(SaasPrescriptionExternalDO::getRxCategory, reqVO.getRxCategory())
            .likeIfPresent(SaasPrescriptionExternalDO::getFixMedicalInstitutionsName, reqVO.getFixMedicalInstitutionsName())
            .betweenIfPresent(SaasPrescriptionExternalDO::getRxOutTime, reqVO.getRxOutTime())
            .eqIfPresent(SaasPrescriptionExternalDO::getPrescriptionType, reqVO.getPrescriptionType())
            .betweenIfPresent(SaasPrescriptionExternalDO::getRxExpireTime, reqVO.getRxExpireTime())
            .eqIfPresent(SaasPrescriptionExternalDO::getLongTerm, reqVO.getLongTerm())
            .eqIfPresent(SaasPrescriptionExternalDO::getElectronicRxSn, reqVO.getElectronicRxSn())
            .eqIfPresent(SaasPrescriptionExternalDO::getDeptPref, reqVO.getDeptPref())
            .eqIfPresent(SaasPrescriptionExternalDO::getOutFlowStatus, reqVO.getOutFlowStatus())
            .likeIfPresent(SaasPrescriptionExternalDO::getDeptName, reqVO.getDeptName())
            .eqIfPresent(SaasPrescriptionExternalDO::getDoctorPref, reqVO.getDoctorPref())
            .eqIfPresent(SaasPrescriptionExternalDO::getRxSignVerifySn, reqVO.getRxSignVerifySn())
            .likeIfPresent(SaasPrescriptionExternalDO::getDoctorName, reqVO.getDoctorName())
            .eqIfPresent(SaasPrescriptionExternalDO::getRxChkBizSn, reqVO.getRxChkBizSn())
            .eqIfPresent(SaasPrescriptionExternalDO::getPharmacistPref, reqVO.getPharmacistPref())
            .eqIfPresent(SaasPrescriptionExternalDO::getRxChkStatus, reqVO.getRxChkStatus())
            .likeIfPresent(SaasPrescriptionExternalDO::getPharmacistName, reqVO.getPharmacistName())
            .betweenIfPresent(SaasPrescriptionExternalDO::getRxChkTime, reqVO.getRxChkTime())
            .eqIfPresent(SaasPrescriptionExternalDO::getMedicareRxNo, reqVO.getMedicareRxNo())
            .eqIfPresent(SaasPrescriptionExternalDO::getMedicareRxTraceCode, reqVO.getMedicareRxTraceCode())
            .eqIfPresent(SaasPrescriptionExternalDO::getMedicareRxStatus, reqVO.getMedicareRxStatus())
            .eqIfPresent(SaasPrescriptionExternalDO::getRequestStatus, reqVO.getRequestStatus())
            .betweenIfPresent(SaasPrescriptionExternalDO::getUploadTime, reqVO.getUploadTime())
            .eqIfPresent(SaasPrescriptionExternalDO::getExt, reqVO.getExt())
            .betweenIfPresent(SaasPrescriptionExternalDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(SaasPrescriptionExternalDO::getId);
    }

}