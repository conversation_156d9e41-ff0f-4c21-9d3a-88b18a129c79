package com.xyy.saas.inquiry.hospital.server.convert.hospital;

import com.xyy.saas.inquiry.enums.doctor.DoctorInquiryTypeEnum;
import com.xyy.saas.inquiry.hospital.api.dept.dto.InquiryHospitalDepartmentRelationDto;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryHospitalDeptDoctorDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDoctorConfigRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDoctorSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryHospitalDeptDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDepartmentRelationDO;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

/**
 * @ClassName：InquiryHospitalDepartmentRelationConvert
 * @Author: xucao
 * @Date: 2024/11/18 9:57
 * @Description: 医院科室转换类
 */
@Mapper
public interface InquiryHospitalDepartmentRelationConvert {

    InquiryHospitalDepartmentRelationConvert INSTANCE = Mappers.getMapper(InquiryHospitalDepartmentRelationConvert.class);

    List<InquiryHospitalDepartmentRelationRespVO> convertDOsTOVOs(List<InquiryHospitalDepartmentRelationDO> inquiryHospitalDepartmentRelationDOs);


    default List<InquiryHospitalDeptDoctorDO> convertTODeptDoctorDOList(InquiryHospitalDepartmentRelationDO inquiryHospitalDepartmentRelationDO, InquiryDoctorDO doctorDO, InquiryHospitalDeptDoctorSaveReqVO saveReqVO) {
        List<InquiryHospitalDeptDoctorDO> result = getDoctorInquiryList(inquiryHospitalDepartmentRelationDO, doctorDO, saveReqVO, DoctorInquiryTypeEnum.MANUAL_INQUIRY);
        result.addAll(getDoctorInquiryList(inquiryHospitalDepartmentRelationDO, doctorDO, saveReqVO, DoctorInquiryTypeEnum.AUTO_INQUIRY));
        return result;
    }

    default List<InquiryHospitalDeptDoctorDO> getDoctorInquiryList(InquiryHospitalDepartmentRelationDO inquiryHospitalDepartmentRelationDO, InquiryDoctorDO doctorDO, InquiryHospitalDeptDoctorSaveReqVO saveReqVO,
        DoctorInquiryTypeEnum doctorInquiryTypeEnum) {
        List<InquiryHospitalDeptDoctorDO> result = new java.util.ArrayList<>();
        List<Integer> inquiryWayType = DoctorInquiryTypeEnum.MANUAL_INQUIRY.equals(doctorInquiryTypeEnum) ? saveReqVO.getInquiryWayType() : saveReqVO.getAutoInquiryWayType();
        if (CollectionUtils.isEmpty(inquiryWayType)) {
            return result;
        }
        inquiryWayType.forEach(wayType -> {
            result.add(InquiryHospitalDeptDoctorDO.builder()
                .hospitalPref(inquiryHospitalDepartmentRelationDO.getHospitalPref())
                .hospitalName(inquiryHospitalDepartmentRelationDO.getHospitalName())
                .deptPref(inquiryHospitalDepartmentRelationDO.getDeptPref())
                .deptName(inquiryHospitalDepartmentRelationDO.getDeptName())
                .hospitalDeptRelationId(inquiryHospitalDepartmentRelationDO.getId())
                .doctorPref(doctorDO.getPref())
                .doctorName(doctorDO.getName())
                .doctorHospitalPref(saveReqVO.getDoctorHospitalPref())
                .inquiryType(doctorInquiryTypeEnum.getCode())
                .inquiryWayType(wayType)
                .autoInquiryTime(Optional.ofNullable(saveReqVO.getAutoInquiryTime()).orElse(List.of()).stream().distinct().toList()).build()
            );
        });
        return result;
    }

    default InquiryHospitalDeptDoctorConfigRespVO convertToConfigRespVO(List<InquiryHospitalDeptDoctorDO> inquiryHospitalDeptDoctorDO) {
        if (inquiryHospitalDeptDoctorDO.isEmpty()) {
            return new InquiryHospitalDeptDoctorConfigRespVO();
        }
        return InquiryHospitalDeptDoctorConfigRespVO.builder()
            .hospitalDeptRelationId(inquiryHospitalDeptDoctorDO.getFirst().getHospitalDeptRelationId())
            .doctorPref(inquiryHospitalDeptDoctorDO.getFirst().getDoctorPref())
            .hospitalPref(inquiryHospitalDeptDoctorDO.getFirst().getHospitalPref())
            .doctorHospitalPref(inquiryHospitalDeptDoctorDO.getFirst().getDoctorHospitalPref())
            .inquiryWayType(inquiryHospitalDeptDoctorDO.stream().filter(doctor -> doctor.getInquiryType().equals(DoctorInquiryTypeEnum.MANUAL_INQUIRY.getCode())).map(InquiryHospitalDeptDoctorDO::getInquiryWayType).distinct().toList())
            .autoInquiryWayType(inquiryHospitalDeptDoctorDO.stream().filter(doctor -> doctor.getInquiryType().equals(DoctorInquiryTypeEnum.AUTO_INQUIRY.getCode())).map(InquiryHospitalDeptDoctorDO::getInquiryWayType).distinct().toList())
            .autoInquiryTime(inquiryHospitalDeptDoctorDO.getFirst().getAutoInquiryTime()).build();
    }

    default List<InquiryHospitalDeptDoctorConfigRespVO> convertToConfigRespVOList(List<InquiryHospitalDeptDoctorDO> inquiryHospitalDeptDoctorDO) {
        List<InquiryHospitalDeptDoctorConfigRespVO> result = new java.util.ArrayList<>();
        if (inquiryHospitalDeptDoctorDO.isEmpty()) {
            return new java.util.ArrayList<>();
        }
        inquiryHospitalDeptDoctorDO.stream().collect(Collectors.groupingBy(InquiryHospitalDeptDoctorDO::getHospitalDeptRelationId)).forEach(
            (key, value) -> {
                result.add(convertToConfigRespVO(value));
            }
        );
        return result;
    }

    List<InquiryHospitalDepartmentRelationDto> convertDOSTODTOS(List<InquiryHospitalDepartmentRelationDO> deptList);

    List<InquiryHospitalDeptDoctorDO> convertDtosToDOs(List<InquiryHospitalDeptDoctorDto> relations);
}
