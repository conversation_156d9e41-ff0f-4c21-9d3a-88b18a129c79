/**
 * @Author:ch<PERSON><PERSON><PERSON>i
 * @Date:2024/09/30 10:49
 */
package com.xyy.saas.inquiry.hospital.server.api.doctor;

import com.xyy.saas.inquiry.hospital.api.doctor.InquiryDoctorApi;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorCardInfoDto;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.hospital.server.config.foward.InquiryDoctorForwardClient;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorPageReqVO;
import com.xyy.saas.inquiry.hospital.server.convert.doctor.InquiryDoctorConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.job.AutoInquiryDoctorJobService;
import com.xyy.saas.inquiry.hospital.server.service.doctor.DoctorPracticeService;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorService;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalDetpDoctorRelationService;
import com.xyy.saas.inquiry.pojo.ForwardResult;
import com.xyy.saas.inquiry.pojo.forward.ForwardPersonInfo;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Optional;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

@DubboService
public class InquiryDoctorApiImpl implements InquiryDoctorApi {

    @Resource
    private InquiryDoctorService inquiryDoctorService;

    @Resource
    private DoctorPracticeService doctorPracticeService;

    @Resource
    private InquiryHospitalDetpDoctorRelationService inquiryHospitalDetpDoctorRelationService;

    @Resource
    private AutoInquiryDoctorJobService autoInquiryDoctorJobService;

    @Resource
    private InquiryDoctorForwardClient inquiryDoctorForwardClient;

    /**
     * 根据userId查询医生信息
     *
     * @param userId
     * @return
     */
    @Override
    public InquiryDoctorDto getInquiryDoctorByUserId(Long userId) {
        return InquiryDoctorConvert.INSTANCE.convertToDto(inquiryDoctorService.getInquiryDoctorByUserId(userId));
    }

    /**
     * 根据医生编码查询信息
     *
     * @param doctorPref
     * @return
     */
    @Override
    public InquiryDoctorDto getInquiryDoctorByDoctorPref(String doctorPref) {
        return InquiryDoctorConvert.INSTANCE.convertToDto(inquiryDoctorService.getRequireInquiryDoctorByDoctorPref(doctorPref));
    }

    /**
     * 根据医生编码查询医生卡片信息
     *
     * @param doctorPref 医生编码
     * @return 医生卡片信息
     */
    @Override
    public InquiryDoctorCardInfoDto getInquiryDoctorCardInfoByDoctorPref(String doctorPref) {
        return inquiryDoctorService.getDoctorCardInfoByDoctorPref(doctorPref);
    }

    /**
     * 已接诊情况下问诊超时取消
     *
     * @param inquiryPref
     */
    @Override
    public void inquiryTimeOutCancelForDoctorReception(String inquiryPref) {
        inquiryDoctorService.inquiryTimeOutCancelForDoctorReception(inquiryPref);
    }

    @Override
    public InquiryDoctorDto getInquiryDoctorSupervision(String pref, String hospitalPref) {
        InquiryDoctorDto inquiryDoctorDto = getInquiryDoctorByDoctorPref(pref);
        // 查医生职称
        Optional.ofNullable(doctorPracticeService.getDoctorPracticeByDoctorId(inquiryDoctorDto.getId())).ifPresent(doctorPracticeDO -> {
            inquiryDoctorDto.setTitleCode(doctorPracticeDO.getTitleCode());
            inquiryDoctorDto.setDoctorMedicareNo(doctorPracticeDO.getDoctorMedicareNo());
        });
        // 医院 医生编码
        Optional.ofNullable(inquiryHospitalDetpDoctorRelationService.getDoctorHospitalPref(pref, hospitalPref)).ifPresent(inquiryDoctorDto::setDoctorHospitalPref);

        return inquiryDoctorDto;
    }

    /**
     * 根据doctorId查询医生信息
     *
     * @param doctorId
     * @return
     */
    @Override
    public InquiryDoctorDto getInquiryDoctorByDoctorId(Long doctorId) {
        return InquiryDoctorConvert.INSTANCE.convertToDto(inquiryDoctorService.getInquiryDoctorByDoctorId(doctorId));
    }

    @Override
    public void jobHandAutoInquiryDoctor() {
        autoInquiryDoctorJobService.jobHandAutoInquiryDoctor();
    }

    @Override
    public List<InquiryDoctorDto> getInquiryDoctorByPrefList(List<String> doctorPrefList) {
        List<InquiryDoctorDO> inquiryDoctorDOList = inquiryDoctorService.getInquiryDoctorList(InquiryDoctorPageReqVO.builder().doctorPrefs(doctorPrefList).build());
        if (CollectionUtils.isEmpty(inquiryDoctorDOList)) {
            return List.of();
        }
        return InquiryDoctorConvert.INSTANCE.convertToDtos(inquiryDoctorDOList);
    }


    @Override
    public ForwardPersonInfo queryUserCaInfo(String mobile) {
        ForwardResult<ForwardPersonInfo> forwardResult = inquiryDoctorForwardClient.queryUserCaInfo(mobile);
        if (forwardResult.isSuccess()) {
            return forwardResult.getResult();
        }
        return null;
    }
}