package com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalReqDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.CollectionUtils;

import java.util.List;


/**
 * 医院信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryHospitalMapper extends BaseMapperX<InquiryHospitalDO> {

    default PageResult<InquiryHospitalDO> selectPage(InquiryHospitalPageReqVO reqVO) {
        return selectPage(reqVO, buildQueryWrapper(reqVO));
    }

    private LambdaQueryWrapperX<InquiryHospitalDO> buildQueryWrapper(InquiryHospitalPageReqVO reqVO) {
        LambdaQueryWrapperX<InquiryHospitalDO> wrapper = new LambdaQueryWrapperX<InquiryHospitalDO>()
            .eqIfPresent(InquiryHospitalDO::getId, reqVO.getId())
            .eqIfPresent(InquiryHospitalDO::getPref, reqVO.getPref())
            .likeIfPresent(InquiryHospitalDO::getName, reqVO.getName())
            .eqIfPresent(InquiryHospitalDO::getInstitutionCode, reqVO.getInstitutionCode())
            .eqIfPresent(InquiryHospitalDO::getLevel, reqVO.getLevel())
            .eqIfPresent(InquiryHospitalDO::getAddress, reqVO.getAddress())
            .eqIfPresent(InquiryHospitalDO::getPhone, reqVO.getPhone())
            .eqIfPresent(InquiryHospitalDO::getEmail, reqVO.getEmail())
            .eqIfPresent(InquiryHospitalDO::getWebsite, reqVO.getWebsite())
            .eqIfPresent(InquiryHospitalDO::getHasMedicare, reqVO.getHasMedicare())
            .eqIfPresent(InquiryHospitalDO::getDisable, reqVO.getDisable())
            .betweenIfPresent(InquiryHospitalDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(InquiryHospitalDO::getId);
        if(!CollectionUtils.isEmpty(reqVO.getUnbindHospitalPrefs())){
            wrapper.notIn(InquiryHospitalDO::getPref, reqVO.getUnbindHospitalPrefs());
        }
        if(StringUtils.isNotBlank(reqVO.getSearchKey())){
            wrapper.like(InquiryHospitalDO::getName, reqVO.getSearchKey())
                .or()
                .eq(InquiryHospitalDO::getPref, reqVO.getSearchKey());
        }
        return wrapper;
    }

    default List<InquiryHospitalDO> getInquiryHospitals(InquiryHospitalReqDto reqDto) {
        return selectList(new LambdaQueryWrapperX<InquiryHospitalDO>()
            .inIfPresent(InquiryHospitalDO::getId, reqDto.getInquiryHospitalIds())
            .inIfPresent(InquiryHospitalDO::getPref, reqDto.getInquiryHospitalPrefs())
            .eqIfPresent(InquiryHospitalDO::getDisable, reqDto.getDisable())
        );
    }
}