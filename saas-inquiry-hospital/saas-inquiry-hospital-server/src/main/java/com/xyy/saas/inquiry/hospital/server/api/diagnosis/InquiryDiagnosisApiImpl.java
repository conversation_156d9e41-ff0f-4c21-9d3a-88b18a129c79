package com.xyy.saas.inquiry.hospital.server.api.diagnosis;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.hospital.api.diagnosis.InquiryDiagnosisApi;
import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDepartmentRelationDto;
import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDepartmentRelationReqDto;
import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisRespVO;
import com.xyy.saas.inquiry.hospital.server.convert.diagnosis.InquiryDiagnosisConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.diagnosis.InquiryDiagnosisDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.diagnosis.InquiryDiagnosisDepartmentRelationDO;
import com.xyy.saas.inquiry.hospital.server.service.diagnosis.InquiryDiagnosisDepartmentRelationService;
import com.xyy.saas.inquiry.hospital.server.service.diagnosis.InquiryDiagnosisService;
import java.util.List;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * @Author:chenxiaoyi
 * @Date:2024/09/30 10:50
 */
@DubboService
@AllArgsConstructor
public class InquiryDiagnosisApiImpl implements InquiryDiagnosisApi {

    private final InquiryDiagnosisService inquiryDiagnosisService;
    private final InquiryDiagnosisDepartmentRelationService inquiryDiagnosisDepartmentRelationService;

    @Override
    public List<InquiryDiagnosisDepartmentRelationDto> queryDiagnosisDepartmentRelation(
        InquiryDiagnosisDepartmentRelationReqDto req) {
        List<InquiryDiagnosisDepartmentRelationDO> relationDOList = inquiryDiagnosisDepartmentRelationService.queryDiagnosisDepartmentRelation(req);
        return BeanUtils.toBean(relationDOList, InquiryDiagnosisDepartmentRelationDto.class);
    }

    @Override
    public List<InquiryDiagnosisDto> queryDiagnosisByCondition(List<String> showNames) {
        if (CollUtil.isEmpty(showNames)) {
            return List.of();
        }
        List<InquiryDiagnosisRespVO> inquiryDiagnosisRespVOS = inquiryDiagnosisService.queryInquiryDiagnosis(InquiryDiagnosisDto.builder().showNames(showNames).build());

        return InquiryDiagnosisConvert.INSTANCE.convertDos(inquiryDiagnosisRespVOS);
    }

    @Override
    public PageResult<InquiryDiagnosisDto> getDiagnosisPage(InquiryDiagnosisDto pageReqDto) {
        InquiryDiagnosisPageReqVO pageReqVO = InquiryDiagnosisConvert.INSTANCE.convertPageDto(pageReqDto);
        PageResult<InquiryDiagnosisDO> pageResult = inquiryDiagnosisService.getDiagnosisPage(pageReqVO);
        return new PageResult<>(
            InquiryDiagnosisConvert.INSTANCE.convertDos(
                InquiryDiagnosisConvert.INSTANCE.convertVos(pageResult.getList())
            ),
            pageResult.getTotal()
        );
    }

    @Override
    public List<InquiryDiagnosisDto> getDiagnosisByName(String showName) {
        if (StringUtils.isBlank(showName)) {
            return List.of();
        }
        List<InquiryDiagnosisRespVO> respVOList = inquiryDiagnosisService.queryInquiryDiagnosisByShowName(showName, CommonStatusEnum.ENABLE.getStatus());
        return InquiryDiagnosisConvert.INSTANCE.convertDos(respVOList);
    }

    @Override
    public List<InquiryDiagnosisDto> getDiagnosisByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return List.of();
        }
        List<InquiryDiagnosisRespVO> diagnosisList = inquiryDiagnosisService.queryInquiryDiagnosis(InquiryDiagnosisDto.builder().ids(ids).status(CommonStatusEnum.ENABLE.getStatus()).build());
        return InquiryDiagnosisConvert.INSTANCE.convertDos(diagnosisList);
    }


}
