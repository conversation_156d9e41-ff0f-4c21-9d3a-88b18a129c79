package com.xyy.saas.inquiry.hospital.server.service.hospital;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.enums.doctor.DoctorTypeEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryHospitalDeptDoctorDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDisableSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDoctorConfigRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDoctorGroupVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDoctorRemoveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDoctorSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDepartmentRelationDO;
import com.xyy.saas.inquiry.mq.doctor.dto.DoctorAutoInquiryTimerWheelDto;
import java.util.List;

/**
 * @ClassName：InquiryHospitalDetpDoctorRelationService
 * @Author: xucao
 * @Date: 2024/11/18 10:51
 * @Description: 医院、科室、医生关系服务
 */
public interface InquiryHospitalDetpDoctorRelationService {

    /**
     * 查询医院科室医生分组信息
     *
     * @param pageReqVO
     * @return
     */
    PageResult<InquiryHospitalDeptDoctorGroupVO> getInquiryHospitalDeptPage(InquiryHospitalDepartmentRelationPageReqVO pageReqVO);

    /**
     * @param hospitalDeptRelationId
     * @return
     */
    List<InquiryDoctorRespVO> getInquiryHospitalDeptDoctorList(InquiryHospitalDepartmentRelationPageReqVO reqVO);

    /**
     * 医院科室下新增医生
     *
     * @param saveReqVO
     * @return
     */
    Boolean addRecord(InquiryHospitalDeptDoctorSaveReqVO saveReqVO);

    /**
     * 启/禁用医院科室
     *
     * @param saveReqVO
     * @return
     */
    Boolean deptDisable(InquiryHospitalDeptDisableSaveReqVO saveReqVO);

    /**
     * 将医生移出科室
     *
     * @param reqVO
     * @return
     */
    Boolean deleteHospitalDeptDoctor(InquiryHospitalDeptDoctorRemoveReqVO reqVO);

    /**
     * 查询医院科室下医生的接诊配置信息
     *
     * @param reqVO
     * @return
     */
    InquiryHospitalDeptDoctorConfigRespVO getDoctorInquiryConfig(InquiryHospitalDepartmentRelationPageReqVO reqVO);


    /**
     * 医院关联科室列表查询
     *
     * @param queryVO
     * @return
     */
    List<InquiryHospitalDepartmentRelationDO> selectHospitalDeptmentList(InquiryHospitalDepartmentRelationPageReqVO queryVO);

    /**
     * 查询医生自动开方时间轮list
     *
     * @param doctorPref
     * @return
     */
    List<DoctorAutoInquiryTimerWheelDto> queryDoctorAutoInquiryTimeWheelDto(List<String> doctorPrefs);

    /**
     * 获取医生所在医院编码
     *
     * @param doctorPref   医生编码
     * @param hospitalPref 医院编码
     * @return
     */
    String getDoctorHospitalPref(String doctorPref, String hospitalPref);

    /**
     * 获取医生所在医院编码
     *
     * @param doctorPref 医生编码
     * @return
     */
    List<String> selectDoctorHospitalList(String doctorPref);

    /**
     * 批量保存医院科室医生关系 根据 hospitalPref + doctorPref + deptPref 判断存在就修改，不存在就新增 采用先删除后新增的策略，确保数据一致性
     *
     * @param relations 医院科室医生关系列表
     */
    void saveHospitalDeptDoctorRelations(List<InquiryHospitalDeptDoctorDto> relations);

    /**
     * 根据医生编码查询医院科室关系列表
     *
     * @param doctorPref 医生编码
     * @return 医院科室医生关系列表
     */
    List<InquiryHospitalDeptDoctorDto> getHospitalDeptDoctorByPref(String doctorPref, String hospitalPref, DoctorTypeEnum doctorTypeEnum);
}
