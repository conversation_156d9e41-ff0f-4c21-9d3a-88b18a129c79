package com.xyy.saas.inquiry.hospital.server.mq.consumer.prescription;// package com.xyy.saas.inquiry.hospital.server.mq.consumer.prescription;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.hospital.server.service.prescription.external.SaasPrescriptionExternalService;
import com.xyy.saas.inquiry.mq.prescription.PrescriptionPricingEvent;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 处方划价 - 处理重庆监管-格林医院
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_hospital_server_mq_consumer_ExternalPrescriptionSupervisionConsumer",
    topic = PrescriptionPricingEvent.TOPIC)
public class PrescriptionPricingSupervisionConsumer {

    @Resource
    private SaasPrescriptionExternalService saasPrescriptionExternalService;

    public static final String GROUP_ID = PrescriptionPricingSupervisionConsumer.class.getName();

    @EventBusListener
    public void externalPrescriptionSupervision(PrescriptionPricingEvent pricingEvent) {
        try {
            saasPrescriptionExternalService.externalSupervision(pricingEvent.getMsg().getPrescriptionPref());
        } catch (Exception e) {
            log.error("externalPrescriptionSupervision error", e);
        }
    }

}
