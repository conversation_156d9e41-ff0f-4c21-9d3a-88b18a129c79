package com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.type.StringListTypeHandler;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 医院医生关系 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_inquiry_hospital_dept_doctor_relation", autoResultMap = true)
@KeySequence("saas_inquiry_hospital_dept_doctor_relation_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryHospitalDeptDoctorDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 医院编码
     */
    private String hospitalPref;
    /**
     * 医院名称
     */
    private String hospitalName;
    /**
     * 科室编码
     */
    private String deptPref;
    /**
     * 科室名称
     */
    private String deptName;

    /**
     * 医院科室关系id
     */
    private Long hospitalDeptRelationId;

    /**
     * 医生编码
     */
    private String doctorPref;
    /**
     * 医生姓名
     */
    private String doctorName;

    /**
     * 医生在当前医院的编码
     */
    private String doctorHospitalPref;

    /**
     * 专业职称代码，例如：2
     */
    private Integer titleCode;

    /**
     * 是否禁用  0否  1是
     */
    private Integer disabled;
    /**
     * 开方类型：0手动开方  1自动开方
     */
    private Integer inquiryType;

    /**
     * 开方方式：1图文 2 视频  3电话
     */
    private Integer inquiryWayType;

    /**
     * 类型,1医生,2药师
     */
    private Integer doctorType;

    /**
     * 自动接诊时段, 隔开  eg:08:30:00-12:00:00,13:00:00-18:00:00
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> autoInquiryTime;

    public void setDoctorInfo(InquiryDoctorDO doctorDO) {
        doctorName = doctorDO.getName();
        doctorPref = doctorDO.getPref();
    }

}