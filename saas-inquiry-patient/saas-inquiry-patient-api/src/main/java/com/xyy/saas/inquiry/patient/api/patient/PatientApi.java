package com.xyy.saas.inquiry.patient.api.patient;

import com.xyy.saas.inquiry.patient.api.patient.dto.InquiryPatientInfoRespDTO;
import com.xyy.saas.inquiry.pojo.migration.MigrationPatientDto;
import java.util.List;

/**
 * @Author: xucao
 * @Date: 2024/12/04 15:45
 * @Description: 患者api
 */
public interface PatientApi {

    void migratePatientInfos(List<MigrationPatientDto> list);

    /**
     * 根据患者编码查询患者信息
     *
     * @param patientPrefs
     * @return
     */
    List<InquiryPatientInfoRespDTO> getPatientInfos(List<String> patientPrefs);

    /**
     * 根据患者编码查询患者信息
     *
     * @param patientPref 患者编码
     * @return 患者信息
     */
    InquiryPatientInfoRespDTO getPatientInfoByPref(String patientPref);

}
