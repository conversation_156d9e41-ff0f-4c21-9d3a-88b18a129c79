package com.xyy.saas.inquiry.patient.controller.app.inquiry;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.ratelimiter.core.annotation.RateLimiter;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantPackageCostApi;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.QuerySourceEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordRespVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.BaseInquiryPatientVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.CheckPatientInfoRespVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.DrugstoreInquiryBoardRespVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.DrugstoreInquiryReqVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryQueueingRespVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryRespVO;
import com.xyy.saas.inquiry.patient.service.inquiry.InquiryAssistService;
import com.xyy.saas.inquiry.patient.service.inquiry.InquiryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Desc 药店问诊相关接口
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/8/28 下午2:37
 */
@Tag(name = "APP+PC -门店去问诊")
@RestController
@RequestMapping(value = {"/admin-api/kernel/patient/inquiry", "/app-api/kernel/patient/inquiry"})
@Validated
@Slf4j
public class DrugstoreInquiryController {

    @Resource
    private TenantPackageCostApi tenantPackageCostApi;

    @Resource
    private InquiryService inquiryService;

    @Resource
    private InquiryAssistService inquiryAssistService;

    // 问诊小助问题以及答复选项查询
    @Operation(summary = "问诊小助问题以及答复选项查询")
    @GetMapping("/inquiry-question-answer")
    public CommonResult<?> getInquiryQuestionAnswer() {
        return success(inquiryService.getInquiryQuestionAnswer());
    }

    // 参保人信息检查
    @Operation(summary = "参保人信息检查")
    @PostMapping("/patient-check")
    public CommonResult<CheckPatientInfoRespVO> patientCheck(@RequestBody BaseInquiryPatientVO patientVO) {
        return inquiryAssistService.checkPatientInfo(patientVO);
    }

    /**
     * 防止小助交互后再提示，又要返回改动
     *
     * @param drugstoreInquiryReqVO
     * @return
     */
    @RateLimiter(message = "问诊频率过高，请稍后再试")
    @Operation(summary = "去问诊(前置参数校验)")
    @PostMapping("/drugstore-inquiry-pre-check")
    public CommonResult<?> drugstoreInquiryPreCheck(@Valid @RequestBody DrugstoreInquiryReqVO drugstoreInquiryReqVO) {
        // 效验问诊权限、额度
        tenantPackageCostApi.isValidTenantPackage(BizTypeEnum.HYWZ, InquiryWayTypeEnum.fromCode(drugstoreInquiryReqVO.getInquiryWayType()), drugstoreInquiryReqVO.getBaseInquiryReqVO().getPrescriptionType());
        // 门店问诊预校验
        return inquiryService.drugstoreInquiryPreCheck(drugstoreInquiryReqVO);
    }


    @Operation(summary = "去问诊(创建问诊单)")
    @PostMapping("/drugstore-inquiry")
    @TraceNode(node = TraceNodeEnum.CREATE_INQUIRY)
    public CommonResult<InquiryRespVO> drugstoreInquiry(@Valid @RequestBody DrugstoreInquiryReqVO drugstoreInquiryReqVO) {
        // 效验问诊权限、额度
        tenantPackageCostApi.isValidTenantPackage(BizTypeEnum.HYWZ, InquiryWayTypeEnum.fromCode(drugstoreInquiryReqVO.getInquiryWayType()), drugstoreInquiryReqVO.getBaseInquiryReqVO().getPrescriptionType());
        // 生成问诊记录单
        return inquiryService.createInquiryRecord(drugstoreInquiryReqVO);
    }

    @Operation(summary = "问诊排队信息查询")
    @GetMapping("/inquiry-queueing")
    public CommonResult<InquiryQueueingRespVO> getInquiryQueueing(@RequestParam("inquiryPref") String inquiryPref) {
        return success(inquiryService.getInquriyQueueing(inquiryPref));
    }

    @Operation(summary = "门店问诊统计看板")
    @GetMapping("/drugstore-board")
    public CommonResult<DrugstoreInquiryBoardRespVO> getDrugstoreBoard() {
        return success(inquiryService.getDrugstoreBoard());
    }


    @Operation(summary = "取消问诊")
    @PutMapping("/inquiry-cancel")
    @TraceNode(node = TraceNodeEnum.PATIENT_CANCEL_INQUIRY, prefLocation = "inquiryPref")
    public CommonResult<Boolean> inquiryCancel(@RequestParam("inquiryPref") String inquiryPref) {
        return success(inquiryService.cancelInquiryRecord(inquiryPref));
    }

    @Operation(summary = "app端问诊记录查询")
    @GetMapping("/inquiryPageList")
    public CommonResult<PageResult<InquiryRecordRespVO>> inquiryPageList(@Valid InquiryRecordPageReqVO pageReqVO) {

        QuerySourceEnum querySourceEnum = QuerySourceEnum.getByCode(pageReqVO.getQuerySourceCode());
        if (querySourceEnum == null) {
            // 设置APP端查询标识
            pageReqVO.isAppQuery();
        } else {
            pageReqVO.setQuerySource(querySourceEnum);
        }

        // 分页查询问诊记录
        PageResult<InquiryRecordRespVO> page = inquiryService.getInquiryRecordPage(pageReqVO);
        // 批量填充问诊记录消息
        inquiryService.assembleMessage(page);
        return success(page);
    }

}

