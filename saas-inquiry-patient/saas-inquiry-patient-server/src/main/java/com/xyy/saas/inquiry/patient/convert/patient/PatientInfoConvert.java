package com.xyy.saas.inquiry.patient.convert.patient;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.api.patient.dto.InquiryPatientInfoRespDTO;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordSaveReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoQueryReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoRespVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.RemoteAuditBaseReqVO.RemotePatientVO;
import com.xyy.saas.inquiry.patient.dal.dataobject.patient.InquiryPatientInfoDO;
import com.xyy.saas.inquiry.patient.util.BusinessUtil;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.migration.MigrationPatientDto;
import com.xyy.saas.inquiry.pojo.patient.PatientSimpleDTO;
import com.xyy.saas.inquiry.util.IdCardUtil;
import com.xyy.saas.inquiry.util.PrefUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @ClassName：PatientInfoConvert
 * @Author: xucao
 * @Date: 2024/10/25 16:02
 * @Description: 患者信息转换器
 */
@Mapper
public interface PatientInfoConvert {

    PatientInfoConvert INSTANCE = Mappers.getMapper(PatientInfoConvert.class);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "pref", expression = "java(com.xyy.saas.inquiry.util.PrefUtil.getPatientPref())")
    @Mapping(target = "name", source = "createReqVO.patientName")
    @Mapping(target = "sex", source = "createReqVO.patientSex")
    @Mapping(target = "age", source = "createReqVO.patientAge")
    @Mapping(target = "mobile", source = "createReqVO.patientMobile")
    @Mapping(target = "idCard", source = "createReqVO.patientIdCard")
    @Mapping(target = "birthday", expression = "java(com.xyy.saas.inquiry.patient.util.BusinessUtil.getBirthdayByIdCard(createReqVO.getPatientIdCard()))")
    @Mapping(target = "source", source = "createReqVO.bizChannelType")
    InquiryPatientInfoDO initConvertInquiryVO2PatientDO(InquiryRecordSaveReqVO createReqVO);

    default InquiryPatientInfoDO convertInquiryDTO2PatientDO(InquiryRecordDto inquiryDto, TenantDto tenantDto) {
        InquiryPatientInfoDO result = InquiryPatientInfoDO.builder()
            .sex(inquiryDto.getPatientSex())
            .name(inquiryDto.getPatientName())
            .age(inquiryDto.getPatientAge())
            .birthday(BusinessUtil.getBirthdayByIdCard(inquiryDto.getPatientIdCard()))
            .mobile(inquiryDto.getPatientMobile())
            .idCard(inquiryDto.getPatientIdCard())
            .source(inquiryDto.getBizChannelType())
            .tenantId(tenantDto.getId())
            .pref(com.xyy.saas.inquiry.util.PrefUtil.getPatientPref())
            .build();
        result.setCreator(inquiryDto.getCreator());
        return result;
    }

    PageResult<InquiryPatientInfoRespVO> convertPage(PageResult<InquiryPatientInfoDO> pageResult);

    PageResult<InquiryPatientInfoRespVO> convertPageInfo(IPage<PatientSimpleDTO> pageResult);

    List<InquiryPatientInfoRespVO> convertList(List<InquiryPatientInfoDO> list);


    InquiryPatientInfoRespVO convertVO(InquiryPatientInfoDO patientInfoDO);

    InquiryPatientInfoPageReqVO convertQueryVO(InquiryPatientInfoQueryReqVO pageReqVO);

    /**
     * 将患者信息VO转换为DTO
     *
     * @param patientInfoRespVO 患者信息VO
     * @return 患者信息DTO
     */
    InquiryPatientInfoRespDTO convertToDTO(InquiryPatientInfoRespVO patientInfoRespVO);

    default InquiryPatientInfoDO convertRemoteAuditPatientVO2DO(RemotePatientVO remotePatientVO) {
        return InquiryPatientInfoDO.builder()
            .pref(StringUtils.isBlank(remotePatientVO.getPatientPref()) ? com.xyy.saas.inquiry.util.PrefUtil.getPatientPref() : remotePatientVO.getPatientPref())
            .name(remotePatientVO.getPatientName())
            .sex(remotePatientVO.getPatientSex())
            .age(remotePatientVO.getPatientAge())
            .mobile(remotePatientVO.getPatientMobile())
            .idCard(remotePatientVO.getPatientIdCard())
            .birthday(BusinessUtil.getBirthdayByIdCard(remotePatientVO.getPatientIdCard()))
            .tenantId(TenantContextHolder.getTenantId())
            .build();
    }

    default List<InquiryPatientInfoDO> convertMigrationList(List<MigrationPatientDto> list) {

        Long nowValue = PrefUtil.getPrefIncrementDelta("getPatientPref", list.size());
        long start = nowValue - list.size() + 1;

        List<InquiryPatientInfoDO> doList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            MigrationPatientDto migrationPatientDto = list.get(i);
            String age;
            if (StringUtils.isBlank(migrationPatientDto.getIdCard())) {
                age = migrationPatientDto.getAge();
            } else {
                try {
                    age = IdCardUtil.getAgeByIdCard(migrationPatientDto.getIdCard());
                } catch (Exception ignore) {
                    age = migrationPatientDto.getAge();
                }
            }
            InquiryPatientInfoDO patientInfoDO = InquiryPatientInfoDO.builder().pref("HZ" + (start + i))
                .name(migrationPatientDto.getUserName())
                .idCard(migrationPatientDto.getIdCard())
                .age(age)
                .sex(Objects.equals(migrationPatientDto.getSex(), (byte) 1) ? 1 : 2)
                .mobile(migrationPatientDto.getTelephone())
                .tenantId(TenantContextHolder.getTenantId())
                .build();
            doList.add(patientInfoDO);
        }
        return doList;
    }

    List<InquiryPatientInfoRespDTO> convertDto(List<InquiryPatientInfoRespVO> list);
}
