package com.xyy.saas.inquiry.patient.service.patient;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoQueryReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoRespVO;
import com.xyy.saas.inquiry.patient.controller.app.patient.vo.PatientInquiryRecordRespVO;
import com.xyy.saas.inquiry.patient.controller.app.patient.vo.PatientMainSuitVO;
import com.xyy.saas.inquiry.patient.dal.dataobject.patient.InquiryPatientInfoDO;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryDto;
import com.xyy.saas.inquiry.pojo.migration.MigrationPatientDto;
import java.util.List;
import com.xyy.saas.inquiry.pojo.migration.MigrationPatientDto;
import com.xyy.saas.inquiry.pojo.migration.MigrationPatientDto;
import java.util.List;

/**
 * @ClassName：InquiryPatientInfoService
 * @Author: xucao
 * @Date: 2024/10/25 13:33
 * @Description: 患者服务接口
 */
public interface InquiryPatientInfoService {


    /**
     * 新增或更新患者信息
     *
     * @param patientInfoDO
     * @return 成功与否
     */
    InquiryPatientInfoDO saveOrUpdatePatientInfo(InquiryPatientInfoDO patientInfoDO, InquiryRecordDto inquiryDto);

    /**
     * 获取患者信息根据编号
     *
     * @param patientPref 编号
     * @return 患者信息
     */
    InquiryPatientInfoRespVO getPatientInfoByPref(String patientPref);

    /**
     * 获得患者信息分页
     *
     * @param pageReqVO 分页查询
     * @return 患者信息分页
     */
    PageResult<InquiryPatientInfoRespVO> getInquiryPatientInfoPage(InquiryPatientInfoQueryReqVO pageReqVO);

    /**
     * 获得患者历史病情
     *
     * @param pageReqVO 分页查询
     * @return 患者病情分页
     */
    PageResult<PatientMainSuitVO> getInquiryPatientMainSuitPage(InquiryPatientInfoPageReqVO pageReqVO);

    /**
     * 新增或更新远程审方患者信息
     *
     * @param patientInfoDO
     * @return
     */
    InquiryPatientInfoDO saveOrUpdateRemoteAuditInquiryPatent(InquiryPatientInfoDO patientInfoDO);

    /**
     * 条件查询患者信息集合
     *
     * @param inquiryPatientInfoPageReqVO 编号
     * @return 患者信息
     */
    List<InquiryPatientInfoRespVO> getPatientInfoListByCondition(InquiryPatientInfoPageReqVO inquiryPatientInfoPageReqVO);

    /**
     * 获取患者最近一次问诊记录
     *
     * @param patientPref
     * @return
     */
    PatientInquiryRecordRespVO getLastInquiryRecord(String patientPref);
    /**
     * 迁移患者信息
     *
     * @param list
     */
    void migratePatientInfos(List<MigrationPatientDto> list);

}
