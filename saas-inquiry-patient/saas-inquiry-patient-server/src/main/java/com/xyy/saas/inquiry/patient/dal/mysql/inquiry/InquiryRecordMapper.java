package com.xyy.saas.inquiry.patient.dal.mysql.inquiry;


import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xyy.saas.inquiry.annotation.forcemaster.ForceMaster;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.QuerySourceEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordGrabbingDoctorDto;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.statistics.vo.InquiryStatisticsReqVO;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDO;
import com.xyy.saas.inquiry.pojo.patient.PatientSimpleDTO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * 问诊记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryRecordMapper extends BaseMapperX<InquiryRecordDO> {

    default PageResult<InquiryRecordDO> selectPage(InquiryRecordPageReqVO reqVO) {
        LambdaQueryWrapperX<InquiryRecordDO> wrapper = new LambdaQueryWrapperX<InquiryRecordDO>()
            .eqIfPresent(InquiryRecordDO::getTenantId, reqVO.getTenantId())
            .inIfPresent(InquiryRecordDO::getTenantId, reqVO.getTenantIds())
            .eqIfPresent(InquiryRecordDO::getPref, reqVO.getPref())
            .eqIfPresent(InquiryRecordDO::getPatientPref, reqVO.getPatientPref())
            .eqIfPresent(InquiryRecordDO::getDoctorPref, reqVO.getDoctorPref())
            .eqIfPresent(InquiryRecordDO::getInquiryStatus, reqVO.getInquiryStatus())
            .inIfPresent(InquiryRecordDO::getInquiryStatus, reqVO.getInquiryStatusList())
            .eqIfPresent(InquiryRecordDO::getInquiryWayType, reqVO.getInquiryWayType())
            .eqIfPresent(InquiryRecordDO::getBizChannelType, reqVO.getBizChannelType())
            .eqIfPresent(InquiryRecordDO::getClientChannelType, reqVO.getClientChannelType())
            .eqIfPresent(InquiryRecordDO::getInquiryBizType, reqVO.getInquiryBizType())
            .eqIfPresent(InquiryRecordDO::getAutoInquiry, reqVO.getAutoInquiry())
            .betweenIfPresent(InquiryRecordDO::getCreateTime, reqVO.getCreateTime())
            .eqIfPresent(InquiryRecordDO::getCreator, reqVO.getCreator())
            .eqIfPresent(InquiryRecordDO::getEnable, reqVO.getEnable())
            .inIfPresent(InquiryRecordDO::getHospitalPref, reqVO.getHospitalPrefs())
            .orderByDesc(InquiryRecordDO::getCreateTime);
        if (ObjectUtil.equals(reqVO.getQuerySource(), QuerySourceEnum.APP)) {
            wrapper.ne(InquiryRecordDO::getInquiryBizType, InquiryBizTypeEnum.REMOTE_INQUIRY.getCode());
        }
        return selectPage(reqVO, wrapper);
    }


    default List<InquiryRecordDO> selectListByCondition(InquiryRecordPageReqVO reqVO) {
        return selectList(
            new LambdaQueryWrapperX<InquiryRecordDO>().eqIfPresent(InquiryRecordDO::getTenantId, reqVO.getTenantId()).eqIfPresent(InquiryRecordDO::getPref, reqVO.getPref()).inIfPresent(InquiryRecordDO::getPref, reqVO.getPrefs())
                .eqIfPresent(InquiryRecordDO::getPatientPref, reqVO.getPatientPref()).eqIfPresent(InquiryRecordDO::getDoctorPref, reqVO.getDoctorPref()).eqIfPresent(InquiryRecordDO::getAutoInquiry, reqVO.getAutoInquiry())
                .eqIfPresent(InquiryRecordDO::getInquiryStatus, reqVO.getInquiryStatus()).eqIfPresent(InquiryRecordDO::getInquiryWayType, reqVO.getInquiryWayType()).eqIfPresent(InquiryRecordDO::getBizChannelType, reqVO.getBizChannelType())
                .eqIfPresent(InquiryRecordDO::getInquiryBizType, reqVO.getInquiryBizType()).betweenIfPresent(InquiryRecordDO::getCreateTime, reqVO.getCreateTime()).orderByDesc(InquiryRecordDO::getCreateTime));
    }

    @ForceMaster
    default InquiryRecordDO selectByPrefForceMaster(String pref) {
        return selectOne(new LambdaQueryWrapperX<InquiryRecordDO>().eq(InquiryRecordDO::getPref, pref));
    }

    // 医生乐观锁抢单接诊
    int doctorGrabbingInquiry(InquiryRecordGrabbingDoctorDto inquiryRecordGrabbingDoctorDto);

    // 医生查询接诊患者分页
    IPage<PatientSimpleDTO> selectDoctorReceptionPatientPage(Page<InquiryPatientInfoPageReqVO> objectPage, @Param("reqVo") InquiryPatientInfoPageReqVO reqVo);

    // 更新问诊结束时间 - 乐观锁
    int updateInquiryEndTime(InquiryRecordDto inquiryRecordDto);

    Long selectCountByDistinctPatient(InquiryStatisticsReqVO inquiryStatisticsReqVO);

    // 批量更新问诊记录
    void batchUpdateInquiryImHistory(@Param("inquiryRecordDOList") List<InquiryRecordDO> inquiryRecordDOList);

    default InquiryRecordDO selectPatientLastInquiryRecord(Long tenantId, String patientPref) {
        return selectOne(new LambdaQueryWrapperX<InquiryRecordDO>().eq(InquiryRecordDO::getTenantId, tenantId).eq(InquiryRecordDO::getPatientPref, patientPref).orderByDesc(InquiryRecordDO::getCreateTime).last("limit 1"));
    }

    default Long selectCountByCondition(InquiryRecordPageReqVO reqVO) {
        return selectCount(
            new LambdaQueryWrapperX<InquiryRecordDO>().eqIfPresent(InquiryRecordDO::getTenantId, reqVO.getTenantId())
                .eqIfPresent(InquiryRecordDO::getPref, reqVO.getPref())
                .inIfPresent(InquiryRecordDO::getPref, reqVO.getPrefs())
                .eqIfPresent(InquiryRecordDO::getPatientPref, reqVO.getPatientPref())
                .eqIfPresent(InquiryRecordDO::getDoctorPref, reqVO.getDoctorPref())
                .eqIfPresent(InquiryRecordDO::getAutoInquiry, reqVO.getAutoInquiry())
                .eqIfPresent(InquiryRecordDO::getInquiryStatus, reqVO.getInquiryStatus())
                .eqIfPresent(InquiryRecordDO::getInquiryWayType, reqVO.getInquiryWayType())
                .eqIfPresent(InquiryRecordDO::getBizChannelType, reqVO.getBizChannelType())
                .eqIfPresent(InquiryRecordDO::getInquiryBizType, reqVO.getInquiryBizType())
                .betweenIfPresent(InquiryRecordDO::getCreateTime, reqVO.getCreateTime()));
    }
}