package com.xyy.saas.inquiry.patient.controller.admin.patient.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryEnum;
import com.xyy.saas.inquiry.enums.patient.PatientQuerySenceEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 患者信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class InquiryPatientInfoPageReqVO extends PageParam {

    @Schema(description = "患者Pref", example = "386")
    private String patientPref;

    @Schema(description = "租户ID", example = "151523122")
    private Long tenantId;

    private List<Long> tenantIds;

    @Schema(description = "患者姓名", example = "赵六")
    private String name;

    @Schema(description = "患者性别：1 男 2 女")
    private Integer sex;

    @Schema(description = "患者年龄")
    private String age;

    @Schema(description = "出生日期")
    private LocalDateTime birthday;

    @Schema(description = "患者手机号")
    private String mobile;

    @Schema(description = "患者身份证号码")
    private String idCard;

    @Schema(description = "患者来源：0、荷叶问诊   1、智慧脸  2、海典ERP")
    private Integer source;

    @Schema(description = "三方系统患者id", example = "19075")
    private String thirdUserId;

    @Schema(description = "接诊医生编码，医生查询接诊患者时需要传")
    private String doctorPref;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "创建人")
    private String creator;


    /**
     * 是否自动开方：0 否  、 1是 {@link AutoInquiryEnum}
     */
    private Integer autoInquiry;
    /**
     * {@link PatientQuerySenceEnum }
     */
    @Schema(description = "数据查询场景")
    private Integer queryScene;

    @Schema(description = "患者Pref集合")
    private List<String> patientPrefList;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}