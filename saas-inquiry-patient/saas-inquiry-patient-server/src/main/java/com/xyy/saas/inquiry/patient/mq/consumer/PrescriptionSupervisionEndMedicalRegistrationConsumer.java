package com.xyy.saas.inquiry.patient.mq.consumer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.mq.prescription.PrescriptionSupervisionEndEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: cxy
 * @Description: 处方对接监管完成 - 问诊就诊登记状态处理mq
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_patient_mq_consumer_PrescriptionSupervisionEndMedicalRegistrationConsumer",
    topic = PrescriptionSupervisionEndEvent.TOPIC)
public class PrescriptionSupervisionEndMedicalRegistrationConsumer {

/*     @Resource
    private MedicalRegistrationService medicalRegistrationService; */


    @EventBusListener
    public void prescriptionSupervisionEnd(PrescriptionSupervisionEndEvent supervisionEndEvent) {
        /* String inquiryPref = supervisionEndEvent.getMsg().getInquiryPref();
        if (StringUtils.isBlank(inquiryPref)) {
            return;
        }
        medicalRegistrationService.updateInquiryMedicalRegistrationStatus(MedicalRegistrationInquiryUpdateDto.builder().inquiryPref(inquiryPref).status(supervisionEndEvent.getMsg().getRegistrationStatusEnum().getCode()).build()); */
    }
}
