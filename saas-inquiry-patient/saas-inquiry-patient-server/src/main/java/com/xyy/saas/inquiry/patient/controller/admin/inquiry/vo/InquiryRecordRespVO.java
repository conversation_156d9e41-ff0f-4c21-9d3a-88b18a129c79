package com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.annotation.InquiryDateType;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryDetailExtDto;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Schema(description = "管理后台 - 问诊记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquiryRecordRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19216")
    private Long id;

    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19216")
    private Long tenantId;

    @Schema(description = "门店名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "19216")
    private String tenantName;

    @Schema(description = "问诊单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("问诊单号")
    private String pref;

    @Schema(description = "患者编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "19102")
    private String patientPref;

    @Schema(description = "患者姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("患者姓名")
    private String patientName;

    @Schema(description = "患者年龄", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("患者年龄")
    private String patientAge;

    @Schema(description = "患者性别 1、男   2、女", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("患者性别 1、男   2、女")
    private Integer patientSex;

    @Schema(description = "患者手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("患者手机号")
    private String patientMobile;

    @Schema(description = "互联网医院编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "14039")
    private String hospitalPref;

    @Schema(description = "互联网医院名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("互联网医院名称")
    private String hospitalName;

    @Schema(description = "科室编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String deptPref;

    @Schema(description = "科室名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("科室名称")
    private String deptName;

    @Schema(description = "处方笺模版编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "22203")
    private Long preTempPId;

    @Schema(description = "医生姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "30134")
    @ExcelProperty("接诊医生")
    private String doctorName;

    @Schema(description = "接诊状态：0 排队中 1、患者取消问诊 2 问诊中 3问诊结束 4医生取消开方 5问诊超时取消", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("接诊状态：0 排队中 1、患者取消问诊 2 问诊中 3问诊结束 4医生取消开方 5问诊超时取消")
    private Integer inquiryStatus;

    @Schema(description = "取消开方原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "不香")
    @ExcelProperty("取消开方原因")
    private String cancelReason;

    @Schema(description = "问诊方式  1、图文问诊  2、视频问诊  3、电话问诊", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("问诊方式  1、图文问诊  2、视频问诊  3、电话问诊")
    private Integer inquiryWayType;

    @Schema(description = "问诊业务类型 1、药店问诊  2、远程审方", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("问诊业务类型 1、药店问诊  2、远程审方")
    private Integer inquiryBizType;

    @Schema(description = "客户端渠类型 0、app  1、pc  2、小程序 ", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer clientChannelType;

    @Schema(description = "客户端系统类型 eg : ios  ,  android", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String clientOsType;

    @Schema(description = "问诊渠道 0、荷叶 1、智慧脸  2、海典ERP", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("问诊渠道 0、荷叶 1、智慧脸  2、海典ERP")
    private Integer bizChannelType;

    @Schema(description = "用药类型：0西药  、1中药", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("用药类型：0西药  、1中药")
    private Integer medicineType;

    @Schema(description = "是否自动开方：0 否  、 1是", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer autoInquiry;

    @Schema(description = "不走自动开方的原因  1、预购药品无用法用量 2、无自动开方医生  3、门店未开通自动开方", example = "不好")
    private Integer unableAutoReason;

    @Schema(description = "IM平台类型  0、腾讯IM", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer imPlatform;

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderNo;

    @Schema(description = "问诊扩展字段")
    private InquiryDetailExtDto ext;

    @Schema(description = "医生接诊时间")
    private LocalDateTime startTime;

    @InquiryDateType("startTime")
    @ExcelProperty("医生接诊时间")
    private String startTimeStr;

    @Schema(description = "问诊结束时间")
    private LocalDateTime endTime;

    @InquiryDateType("endTime")
    @ExcelProperty("问诊结束时间")
    private String endTimeStr;

    @Schema(description = "录音地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    private String mp3Url;

    @Schema(description = "MP3状态 0 未视频 1 视频中 2 已合流 3 已编码 4 已完成", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer mp3Status;

    @Schema(description = "视频地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    private String mp4Url;

    @Schema(description = "im问诊记录", requiredMode = Schema.RequiredMode.REQUIRED)
    private String imPdf;

    @Schema(description = "视频混流id", requiredMode = Schema.RequiredMode.REQUIRED, example = "72")
    private String streamId;

    @Schema(description = "任务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18661")
    private String transcodingId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @InquiryDateType("createTime")
    @ExcelProperty("创建时间")
    private String createTimeStr;


    @Schema(description = "肝肾功能异常  0、无  1、肝功能异常  2、肾功能异常  3、肝肾功能异常", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("肝肾功能异常  0、无  1、肝功能异常  2、肾功能异常  3、肝肾功能异常")
    private Integer liverKidneyValue;

    @Schema(description = "妊娠哺乳期   0、否  1、妊娠期   2、哺乳期  3、妊娠哺乳期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("妊娠哺乳期   0、否  1、妊娠期   2、哺乳期  3、妊娠哺乳期")
    private Integer gestationLactationValue;

    @Schema(description = "慢病病情需要 0 否  1是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("慢病病情需要 0 否  1是")
    private Integer slowDisease;

    @Schema(description = "主诉")
    @ExcelProperty("主诉")
    private List<String> mainSuit;

    @Schema(description = "过敏史  eg：青霉素|头孢")
    @ExcelProperty("过敏史  eg：青霉素|头孢")
    private List<String> allergicItem;

    @Schema(description = "诊断编码")
    @ExcelProperty("诊断编码")
    private List<String> diagnosisCode;

    @Schema(description = "诊断说明", example = "王五")
    @ExcelProperty("诊断说明")
    private List<String> diagnosisName;

    @Schema(description = "个人史")
    @ExcelProperty("个人史")
    private String patientHisDesc;

    @Schema(description = "现病史")
    @ExcelProperty("现病史")
    private String currentIllnessDesc;

    @Schema(description = "线下就医处方或病历图片")
    private List<String> offlinePrescriptions;

    @Schema(description = "预购药明细")
    private InquiryProductDto preDrugDetail;

    @Schema(description = "备注说明")
    private String remarks;

    @Schema(description = "最后一条消息", requiredMode = Schema.RequiredMode.REQUIRED, example = "请您开具处方")
    private String lastMsg;

    @Schema(description = "未读消息数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "3")
    private Integer unReadCount;

    @Schema(description = "问诊聚合状态 1-进行中, 2-待接诊, 3-已完成")
    private Integer inquiryGroupStatus;

    /**
     * 日期类型 1-年月日时分秒， 2-年月日
     */
    private Integer dateType;


    @Schema(description = "处方类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "3")
    private Integer prescriptionType;


}