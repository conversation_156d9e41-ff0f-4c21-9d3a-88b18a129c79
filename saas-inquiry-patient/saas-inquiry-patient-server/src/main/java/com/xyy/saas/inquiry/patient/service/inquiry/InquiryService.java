package com.xyy.saas.inquiry.patient.service.inquiry;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordGrabbingDoctorDto;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordDetailRespVO;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordRespVO;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordSaveReqVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.DrugstoreInquiryBoardRespVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.DrugstoreInquiryReqVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryQueueingRespVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryRespVO;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDetailDO;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/8/28 下午2:32
 */
public interface InquiryService {


    /**
     * 门店创建问诊单提前校验参数
     *
     * @param drugstoreInquiryReqVO
     * @return
     */
    CommonResult<?> drugstoreInquiryPreCheck(DrugstoreInquiryReqVO drugstoreInquiryReqVO);


    /**
     * 创建问诊单
     *
     * @param drugstoreInquiryReqVO
     * @return
     */
    CommonResult<InquiryRespVO> createInquiryRecord(DrugstoreInquiryReqVO drugstoreInquiryReqVO);


    /**
     * 取消问诊单
     *
     * @param inquiryRecordGuid
     * @return
     */
    Boolean cancelInquiryRecord(String inquiryRecordGuid);


    /**
     * 更新问诊记录
     *
     * @param updateReqVO 更新信息
     */
    void updateInquiryRecord(@Valid InquiryRecordSaveReqVO updateReqVO);

    /**
     * 删除问诊记录
     *
     * @param id 编号
     */
    void deleteInquiryRecord(Long id);

    /**
     * 获得问诊记录
     *
     * @param id id
     * @return
     */
    InquiryRecordDetailRespVO getInquiryRecord(Long id);

    /**
     * 获得问诊记录
     *
     * @param pref 编号
     * @return 问诊记录
     */
    InquiryRecordDetailRespVO getInquiryRecordDetailVO(String pref);


    /**
     * 获得问诊记录
     *
     * @param pref 问诊单号
     * @return 问诊记录
     */
    InquiryRecordDO getInquiryByPref(String pref);

    /**
     * 获得问诊记录
     *
     * @param pref 问诊单号
     * @return 问诊记录
     */
    InquiryRecordDto getInquiryDtoByPref(String pref);

    /**
     * 批量获得问诊记录
     *
     * @param inquiryPrefs
     * @return
     */
    List<InquiryRecordDto> getInquiryDtoByPrefs(List<String> inquiryPrefs);

    /**
     * 获得问诊记录详情
     *
     * @param pref 问诊单号
     * @return 问诊记录
     */
    InquiryRecordDetailDO getInquiryRecordDetailByPref(String pref);

    /**
     * 批量获得问诊记录详情
     *
     * @param prefs
     * @return
     */
    List<InquiryRecordDetailDO> getInquiryRecordDetailByPrefs(List<String> prefs);

    /**
     * 获得问诊记录分页
     *
     * @param pageReqVO 分页查询
     * @return 问诊记录分页
     */
    PageResult<InquiryRecordRespVO> getInquiryRecordPage(InquiryRecordPageReqVO pageReqVO);

    /**
     * 获得问诊记录列表
     *
     * @param pageReqVO 查询条件
     * @return 问诊单列表
     */
    List<InquiryRecordDO> getInquiryRecordList(InquiryRecordPageReqVO pageReqVO);

    /**
     * 批量填充问诊记录
     *
     * @param page
     */
    void assembleMessage(PageResult<InquiryRecordRespVO> page);

    Boolean updateInquiry(InquiryRecordDto inquiryRecordDto);

    /**
     * 医生接诊 - 乐观锁
     *
     * @param inquiryRecordGrabbingDoctorDto 医生接诊dto
     * @return true-成功 false-失败
     */
    boolean doctorGrabbingInquiry(@Valid InquiryRecordGrabbingDoctorDto inquiryRecordGrabbingDoctorDto);

    InquiryQueueingRespVO getInquriyQueueing(String inquiryPref);

    /**
     * 查询门店问诊统计看板
     *
     * @return 统计看板参数
     */
    DrugstoreInquiryBoardRespVO getDrugstoreBoard();

    /**
     * 查询问诊问题以及答案
     *
     * @return
     */
    Map<String, Object> getInquiryQuestionAnswer();

    /**
     * 获取问诊详情
     *
     * @param recordDO
     * @return
     */
    InquiryRecordDetailRespVO getInquiryRecordDetailRespVO(InquiryRecordDO recordDO);

    /**
     * 乐观锁修改问诊结束时间
     *
     * @param inquiryRecordDto
     * @return
     */
    boolean updateInquiryEndTime(InquiryRecordDto inquiryRecordDto);


    InquiryRecordDto getInquiryDtoByInquiryPref(String inquiryPref);

    /**
     * @param pref
     * @return
     */
    String getInquiryImRecord(String pref);

    /**
     * 批量修改问诊单
     *
     * @param updateRecords
     */
    void updateInquirys(List<InquiryRecordDto> updateRecords);

}
