package com.xyy.saas.inquiry.patient.service.dispatch;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_DISABLE;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.INQUIRY_RECORD_AGE_RANGE_FAIL;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.INQUIRY_RECORD_ID_CARD_FAIL;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.INQUIRY_RECORD_PREGNANCY_FAIL;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.PRODUCT_INCLUDE_SPECIAL_SCOPE;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.TENANT_TYPE_CANNOT_INQUIRY_FAIL;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.THIRD_PARTY_PRE_INQUIRY_IS_DELETE;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.THIRD_PARTY_REPETITION_INQUIRY;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.constant.ProductConstant;
import com.xyy.saas.inquiry.drugstore.api.option.InquiryOptionConfigApi;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigRespDto;
import com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum;
import com.xyy.saas.inquiry.enums.patient.PregnancyLactationEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.hospital.api.diagnosis.InquiryDiagnosisApi;
import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDepartmentRelationDto;
import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDepartmentRelationReqDto;
import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.convert.inquiry.InquiryRecordConvert;
import com.xyy.saas.inquiry.patient.dal.dataobject.third.ThirdPartyPreInquiryDO;
import com.xyy.saas.inquiry.patient.dal.mysql.third.ThirdPartyPreInquiryMapper;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.product.api.ProductStdlibApi;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductSearchDto;
import com.xyy.saas.inquiry.util.IdCardUtil;
import com.xyy.saas.transmitter.api.dict.TransmissionOrganDictApi;
import com.xyy.saas.transmitter.api.dict.dto.TransmissionOrganDictDTO;
import com.xyy.saas.transmitter.api.transmission.TransmissionConfigApi;
import com.xyy.saas.transmitter.enums.DictTypeConstants;
import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/12/20 11:05
 * @Description: 问诊参数前置校验
 * @see InquiryAssignHospital   next 选定互联网医院
 */
@Component
@Slf4j
public class InquiryPreCheck extends InquiryBaseDispatch {

    @Resource
    private TenantApi tenantApi;

    @Resource
    private InquiryDiagnosisApi inquiryDiagnosisApi;

    @Resource
    private InquiryOptionConfigApi inquiryOptionConfigApi;

    @Resource
    private ThirdPartyPreInquiryMapper thirdPartyPreInquiryMapper;

    @DubboReference
    private ProductStdlibApi productStdlibApi;

    @Resource
    private ConfigApi configApi;

    @DubboReference
    private TransmissionConfigApi transmissionConfigApi;

    @DubboReference
    private TransmissionOrganDictApi transmissionOrganDictApi;

    @Override
    @TraceNode(node = TraceNodeEnum.PRE_CHECK, prefLocation = "inquiryDto.pref")
    public void execute(InquiryRecordDto inquiryDto) {
        log.info("问诊单号：{},开始执行问诊单调度责任链【参数预检】", inquiryDto.getPref());
        // 1、获取租户信息
        TenantDto tenantDto = tenantApi.getTenant();
        // 2、参数校验
        checkParam(inquiryDto, tenantDto);
        // 3、给问诊单设置患者信息、租户信息
        InquiryRecordConvert.INSTANCE.setInquiryPatientInfoAndTenant(inquiryDto, tenantDto);
        // 4、查询并设置诊断关联科室
        setDiagnosisDept(inquiryDto);
    }

    /**
     * 查询并设置诊断关联科室
     *
     * @param inquiryDto 问诊信息
     */
    private void setDiagnosisDept(InquiryRecordDto inquiryDto) {
        List<String> diagnosisCodes = inquiryDto.getInquiryRecordDetailDto().getDiagnosisCode();

        Integer organId = transmissionConfigApi.diagnosisChangeQueryCatalog(TenantContextHolder.getTenantId(), inquiryDto.getInquiryRecordDetailDto().getPrescriptionType());
        // 根据门店配置，查询诊断是否切换数据源,如果是，这里是三方的诊断编码
        if (organId != null && CollUtil.isNotEmpty(diagnosisCodes)) {

            List<Long> saasDictIds = transmissionOrganDictApi.queryMatchSaasDictByValues(TransmissionOrganDictDTO.builder().organId(organId).dictType(DictTypeConstants.DIAGNOSIS_DICT).values(diagnosisCodes).build());
            log.info("问诊单号：{},诊断编码：{},查询到匹配的saasDictIds：{}", inquiryDto.getPref(), diagnosisCodes, saasDictIds);
            diagnosisCodes = inquiryDiagnosisApi.getDiagnosisByIds(saasDictIds).stream().map(InquiryDiagnosisDto::getDiagnosisCode).toList();
        }
        // 根据诊断查询科室列表
        List<InquiryDiagnosisDepartmentRelationDto> relationDtos = inquiryDiagnosisApi.queryDiagnosisDepartmentRelation(InquiryDiagnosisDepartmentRelationReqDto.builder().diagnosisCodes(diagnosisCodes).build());
        // 为本次问诊设置可选科室列表
        inquiryDto.setChoiceDeptList(relationDtos.stream().map(InquiryDiagnosisDepartmentRelationDto::getDeptPref).toList());
    }


    /**
     * 参数校验
     *
     * @param inquiryDto 请求参数
     */
    public void checkParam(InquiryRecordDto inquiryDto, TenantDto tenantDto) {
        // 门店启用状态
        if (CommonStatusEnum.isDisable(tenantDto.getStatus())) {
            throw exception(TENANT_DISABLE, tenantDto.getName());
        }
        // 当前机构为连锁总部，不支持发起问诊
        if (Objects.equals(tenantDto.getWzTenantType(), TenantTypeEnum.CHAIN_HEADQUARTERS)) {
            throw exception(TENANT_TYPE_CANNOT_INQUIRY_FAIL);
        }
        // 校验身份找是否合法
        String idCard = inquiryDto.getPatientIdCard();
        if (StringUtils.isNotBlank(idCard) && !IdCardUtil.validateCard(idCard)) {
            throw exception(INQUIRY_RECORD_ID_CARD_FAIL);
        }
        // 全局合规性参数校验
        InquiryOptionConfigRespDto optionConfig = inquiryOptionConfigApi.getInquiryOptionConfig(tenantDto, InquiryOptionTypeEnum.PROC_INQUIRY_COMPLIANCE);
        if (BooleanUtil.isTrue(optionConfig.getProcInquiryCompliance())) {
            if (!(NumberUtil.parseInt(inquiryDto.getPatientAge()) >= Optional.ofNullable(optionConfig.getProcInquiryComplianceForPatientAgeGe()).orElse(0)
                && NumberUtil.parseInt(inquiryDto.getPatientAge()) < Optional.ofNullable(optionConfig.getProcInquiryComplianceForPatientAgeLt()).orElse(999))) {
                throw exception(INQUIRY_RECORD_AGE_RANGE_FAIL);
            }
            if (BooleanUtil.isFalse(optionConfig.getProcInquiryComplianceAllowForPregnancyLactation())
                && inquiryDto.getInquiryRecordDetailDto().getGestationLactationValue() != null
                && !Objects.equals(inquiryDto.getInquiryRecordDetailDto().getGestationLactationValue(), PregnancyLactationEnum.NORMAL.getCode())) {
                throw exception(INQUIRY_RECORD_PREGNANCY_FAIL);
            }
        }
        if (inquiryDto.getThirdPartyPreInquiryId() != null) {
            ThirdPartyPreInquiryDO thirdPartyPreInquiryDO = thirdPartyPreInquiryMapper.selectOne(
                new LambdaQueryWrapperX<ThirdPartyPreInquiryDO>().eqIfPresent(ThirdPartyPreInquiryDO::getTenantId, TenantContextHolder.getTenantId()).eqIfPresent(ThirdPartyPreInquiryDO::getId, inquiryDto.getThirdPartyPreInquiryId())
                    .eq(ThirdPartyPreInquiryDO::getDeleted, false));
            if (thirdPartyPreInquiryDO == null) {
                throw exception(THIRD_PARTY_PRE_INQUIRY_IS_DELETE);
            }
            if (StringUtils.isNotBlank(thirdPartyPreInquiryDO.getInquiryPref())) {
                throw exception(THIRD_PARTY_REPETITION_INQUIRY);
            }
        }
        // 校验预购药是否包含毒麻精放 , 如果包含则身份证必填
        if (checkProductIncludeSpecialScope(inquiryDto) && StringUtils.isBlank(idCard)) {
            throw exception(PRODUCT_INCLUDE_SPECIAL_SCOPE);
        }

    }

    /**
     * 校验预购药是否包含毒麻精放
     *
     * @param inquiryDto
     * @return
     */
    private boolean checkProductIncludeSpecialScope(InquiryRecordDto inquiryDto) {

        // 获取去问诊需要校验身份证必填的药品类型
        List<String> checkIdCardMustFillBusinessScopeList = this.getCheckIdCardMustFillBusinessScopeList();

        if (CollUtil.isEmpty(checkIdCardMustFillBusinessScopeList) || inquiryDto.getInquiryRecordDetailDto() == null
            || inquiryDto.getInquiryRecordDetailDto().getPreDrugDetail() == null || CollUtil.isEmpty(inquiryDto.getInquiryRecordDetailDto().getPreDrugDetail().getInquiryProductInfos())) {
            return false;
        }

        List<Long> prefList = inquiryDto.getInquiryRecordDetailDto().getPreDrugDetail().getInquiryProductInfos().stream()
            .filter(item -> StringUtils.isNotBlank(item.getPref()) && Convert.toLong(item.getPref()) != null)
            .map(item -> Convert.toLong(item.getPref())).distinct().toList();

        if (CollUtil.isEmpty(prefList)) {
            return false;
        }

        StdlibProductSearchDto searchDto = new StdlibProductSearchDto().setIdList(prefList);
        List<ProductStdlibDto> productStdlibDtoList = productStdlibApi.searchProductStdlibList(searchDto, prefList.size());

        if (CollUtil.isEmpty(productStdlibDtoList)) {
            return false;
        }

        // 去问诊需要校验身份证必填的二级分类类型
        List<String> checkIdCardMustFillSecondCategoryList = this.getCheckIdCardMustFillSecondCategoryList();

        for (ProductStdlibDto item : productStdlibDtoList) {
            // 校验药品的二级分类是否在配置中
            if (CollUtil.isNotEmpty(checkIdCardMustFillSecondCategoryList)
                && StringUtils.isNotBlank(item.getSecondCategory())
                && checkIdCardMustFillSecondCategoryList.contains(item.getSecondCategory())) {
                return true;
            }
            if (StringUtils.isBlank(item.getBusinessScope())) {
                continue;
            }
            // 校验药品的经营范围类型是否在配置中
            if (Arrays.stream(item.getBusinessScope().split(",")).anyMatch(checkIdCardMustFillBusinessScopeList::contains)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取去问诊需要校验身份证必填的药品类型
     *
     * @return
     */
    private List<String> getCheckIdCardMustFillBusinessScopeList() {

        String checkIdCardMustFillBusinessScopeList = configApi.getConfigValueByKey(ProductConstant.CHECK_ID_CARD_MUST_FILL_BUSINESS_SCOPE_LIST);

        if (StringUtils.isBlank(checkIdCardMustFillBusinessScopeList)) {
            return Lists.newArrayList();
        }

        return JSONObject.parseArray(checkIdCardMustFillBusinessScopeList, String.class);
    }

    /**
     * 获取去问诊需要校验身份证必填的二级分类集合
     *
     * @return
     */
    private List<String> getCheckIdCardMustFillSecondCategoryList() {

        String checkIdCardMustFillSecondCategoryList = configApi.getConfigValueByKey(ProductConstant.CHECK_ID_CARD_MUST_FILL_SECOND_CATEGORY_LIST);

        if (StringUtils.isBlank(checkIdCardMustFillSecondCategoryList)) {
            return Lists.newArrayList();
        }

        return JSONObject.parseArray(checkIdCardMustFillSecondCategoryList, String.class);
    }
}
