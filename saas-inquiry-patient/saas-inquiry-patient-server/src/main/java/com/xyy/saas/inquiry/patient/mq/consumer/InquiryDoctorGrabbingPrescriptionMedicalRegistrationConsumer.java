package com.xyy.saas.inquiry.patient.mq.consumer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.mq.inquiry.InquiryDoctorGrabbingPrescriptionEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: cxy
 * @Description: 医生抢单成功- 问诊就诊登记状态处理mq
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_patient_mq_consumer_InquiryDoctorGrabbingPrescriptionMedicalRegistrationConsumer",
    topic = InquiryDoctorGrabbingPrescriptionEvent.TOPIC)
public class InquiryDoctorGrabbingPrescriptionMedicalRegistrationConsumer {

    /* @Resource
    private MedicalRegistrationService medicalRegistrationService; */


    @EventBusListener
    public void inquiryDoctorGrabbingPrescription(InquiryDoctorGrabbingPrescriptionEvent inquiryEndEvent) {
        // String inquiryPref = inquiryEndEvent.getMsg().getInquiryPref();
        // if (StringUtils.isBlank(inquiryPref)) {
        //     return;
        // }
        // medicalRegistrationService.updateInquiryMedicalRegistrationStatus(MedicalRegistrationInquiryUpdateDto.builder().inquiryPref(inquiryPref).status(inquiryEndEvent.getMsg().getRegistrationStatusEnum().getCode()).build());
    }
}
