package com.xyy.saas.inquiry.patient.service.inquiry;

import com.xyy.saas.inquiry.enums.doctor.AuditStatusEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordPageReqVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryRespVO;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.third.ThirdPartyPreInquiryDO;
import java.util.List;

/**
 * @Author: xucao
 * @DateTime: 2025/4/15 14:01
 * @Description: 问诊IM服务接口
 **/
public interface InquiryImService {

    /**
     * 问诊单变更批量通知已派单医生刷新工作台
     * @param doctorList
     */
    void batchNotifyDoctorForInquiryChange(List<String> doctorList);


    /**
     * 接诊相关消息生成
     * @param inquiryRecordDO
     */
    void generateAutomaticChatRecord(InquiryRecordDO inquiryRecordDO);


    /**
     * 三方预问诊单审核消息发送
     * @param preInquiryDO 预问诊对象
     * @param auditStatusEnum 审核结果
     */
    void sendPreInquiryAuditMessage(ThirdPartyPreInquiryDO preInquiryDO , InquiryRespVO inquiryRespVO, AuditStatusEnum auditStatusEnum);

    /**
     * 批量更新问诊单
     *
     * @param inquiryRecordDtoList
     * @return
     */
    boolean batchUpdateInquiryImHistory(List<InquiryRecordDto> inquiryRecordDtoList);

    /**
     * 刷新im历史聊天记录
     *
     * @param inquiryRecordPageReqVO
     * @return
     */
    Long flushImHistory(InquiryRecordPageReqVO inquiryRecordPageReqVO);
}
