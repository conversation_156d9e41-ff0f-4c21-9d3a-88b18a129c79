package com.xyy.saas.inquiry.patient.api.patient;

import cn.hutool.core.collection.CollUtil;
import com.xyy.saas.inquiry.patient.api.patient.dto.InquiryPatientInfoRespDTO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoRespVO;
import com.xyy.saas.inquiry.patient.convert.patient.PatientInfoConvert;
import com.xyy.saas.inquiry.patient.api.patient.dto.InquiryPatientInfoRespDTO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoRespVO;
import com.xyy.saas.inquiry.patient.convert.patient.PatientInfoConvert;
import com.xyy.saas.inquiry.patient.service.patient.InquiryPatientInfoService;
import com.xyy.saas.inquiry.pojo.migration.MigrationPatientDto;
import jakarta.annotation.Resource;
import java.util.List;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * @Author:chenxiaoyi
 * @Date:2025/06/09 17:17
 */
@DubboService
public class PatientApiImpl implements PatientApi {

    @Resource
    private InquiryPatientInfoService inquiryPatientInfoService;

    @Override
    public void migratePatientInfos(List<MigrationPatientDto> list) {
        inquiryPatientInfoService.migratePatientInfos(list);
    }

    @Override
    public List<InquiryPatientInfoRespDTO> getPatientInfos(List<String> patientPrefs) {
        if (CollUtil.isEmpty(patientPrefs)) {
            return List.of();
        }
        InquiryPatientInfoPageReqVO pageReqVO = new InquiryPatientInfoPageReqVO().setPatientPrefList(patientPrefs);
        List<InquiryPatientInfoRespVO> list = inquiryPatientInfoService.getPatientInfoListByCondition(pageReqVO);
        return PatientInfoConvert.INSTANCE.convertDto(list);
    }

    @Override
    public InquiryPatientInfoRespDTO getPatientInfoByPref(String patientPref) {
        InquiryPatientInfoRespVO patientInfo = inquiryPatientInfoService.getPatientInfoByPref(patientPref);
        return PatientInfoConvert.INSTANCE.convertToDTO(patientInfo);
    }
}
