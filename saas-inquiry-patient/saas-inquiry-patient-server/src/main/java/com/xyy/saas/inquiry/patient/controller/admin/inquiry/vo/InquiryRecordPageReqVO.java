package com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.xyy.saas.inquiry.enums.inquiry.QuerySourceEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 问诊记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquiryRecordPageReqVO extends PageParam {

    @Schema(description = "租户id")
    private Long tenantId;

    private List<Long> tenantIds;

    @Schema(description = "问诊单号")
    private String pref;

    @Schema(description = "问诊单号列表")
    private List<String> prefs;

    @Schema(description = "患者编码", example = "19102")
    private String patientPref;

    @Schema(description = "患者姓名", example = "19102")
    private String patientName;

    @Schema(description = "医生编码", example = "30134")
    private String doctorPref;

    @Schema(description = "问诊状态：0 排队中 1、患者取消问诊 2 问诊中 3问诊结束 4医生取消开方 5问诊超时取消", example = "2")
    private Integer inquiryStatus;

    @Schema(description = "问诊方式  1、图文问诊  2、视频问诊  3、电话问诊", example = "2")
    private Integer inquiryWayType;

    @Schema(description = "问诊业务类型 1、药店问诊  2、远程审方", example = "2")
    private Integer inquiryBizType;

    @Schema(description = "问诊渠道 0、荷叶 1、智慧脸  2、海典ERP", example = "1")
    private Integer bizChannelType;

    @Schema(description = "客户端渠道类型 0、app 1、pc  2、小程序", example = "0")
    private Integer clientChannelType;

    @Schema(description = "是否自动开方  0 否  1是", example = "0")
    private Integer autoInquiry;

    @Schema(description = "第三方预问诊id")
    private Long thirdPartyPreInquiryId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "查询类型")
    private QuerySourceEnum querySource;

    @Schema(description = "前端查询类型code")
    private Integer querySourceCode;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "问诊聚合状态 1-进行中, 2-待接诊, 3-已完成")
    private Integer inquiryGroupStatus;

    @Schema(description = "问诊状态：0 排队中 1、患者取消问诊 2 问诊中 3问诊结束 4医生取消开方 5问诊超时取消", example = "2")
    private List<Integer> inquiryStatusList;

    /**
     * 数据状态 0 有效 1 作废 {@link CommonStatusEnum}
     */
    private Integer enable;

    /**
     * 日期类型 1-年月日时分秒， 2-年月日
     */
    private Integer dateType;

    public void isAppQuery() {
        this.querySource = QuerySourceEnum.APP;
    }

    public void isWebQuery() {
        this.querySource = QuerySourceEnum.WEB;
    }

    /**
     * 是否是医院员工查询
     */
    private boolean hospitalEmpQuery;

    /**
     * 医院prefs
     */
    private List<String> hospitalPrefs;
}