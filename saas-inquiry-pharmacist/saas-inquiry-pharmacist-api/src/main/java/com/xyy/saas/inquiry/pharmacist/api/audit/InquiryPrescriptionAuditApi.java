package com.xyy.saas.inquiry.pharmacist.api.audit;

import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.pharmacist.api.audit.dto.InquiryPrescriptionAuditDto;
import com.xyy.saas.inquiry.pharmacist.api.audit.dto.InquiryPrescriptionAuditPageReqDto;
import com.xyy.saas.inquiry.pharmacist.api.audit.dto.RemotePrescriptionAuditDto;
import java.util.List;

/**
 * @Author:chenxiaoyi
 * @Date:2024/12/26 11:49
 */
public interface InquiryPrescriptionAuditApi {

    /**
     * 远程审方推入审方池
     *
     * @param auditDto 远程审方参数
     */
    void remotePrescriptionPushAuditPool(RemotePrescriptionAuditDto auditDto);

    /**
     * 远程审方移除审方池
     *
     * @param auditDto 远程审方参数
     */
    void remotePrescriptionRemoveAuditPool(RemotePrescriptionAuditDto auditDto);

    /**
     * 批量 远程审方移除审方池
     *
     * @param auditDto 远程审方参数
     */
    void remotePrescriptionBatchRemoveAuditPool(RemotePrescriptionAuditDto auditDto);

    /**
     * 查询审方列表
     *
     * @param inquiryPrescriptionAuditPageReqDto
     * @return
     */
    List<InquiryPrescriptionAuditDto> selectListByCondition(InquiryPrescriptionAuditPageReqDto inquiryPrescriptionAuditPageReqDto);

    /**
     * 根据药师类型查询审方列表
     *
     * @param pref
     * @param auditorType
     * @return
     */
    InquiryPrescriptionAuditDto selectByAuditorType(String pref, AuditorTypeEnum auditorType);
}
