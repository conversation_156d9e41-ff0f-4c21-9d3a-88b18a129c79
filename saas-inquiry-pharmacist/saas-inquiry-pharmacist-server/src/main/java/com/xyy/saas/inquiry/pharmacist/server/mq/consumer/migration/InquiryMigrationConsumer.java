package com.xyy.saas.inquiry.pharmacist.server.mq.consumer.migration;

import cn.iocoder.yudao.framework.common.util.monitor.TraceIdUtil;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.framework.web.core.util.LoginUserContextUtils;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.migration.InquiryMigrationEvent;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.migration.InquiryMigrationMsgDto;
import com.xyy.saas.inquiry.pharmacist.server.service.migration.MigrationCoreService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 问诊迁移 Consumer
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/03 16:29
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_pharmacist_server_mq_consumer_migration_InquiryMigrationConsumer",
    topic = InquiryMigrationEvent.TOPIC)
public class InquiryMigrationConsumer {

    @Resource
    private MigrationCoreService migrationCoreService;

    @EventBusListener
    public void inquiryMigrationConsumer(InquiryMigrationEvent inquiryMigrationEvent) {
        // traceId
        TraceIdUtil.putDefaultIfBlank(null);

        log.info("SecurityFrameworkUtils={} , LoginUserContextUtils = {}", SecurityFrameworkUtils.getLoginUserId(), LoginUserContextUtils.getLoginUserId());

        InquiryMigrationMsgDto msg = inquiryMigrationEvent.getMsg();

        migrationCoreService.migration(msg.getOrganSign());

        TraceIdUtil.removeTraceId();
    }
}
