package com.xyy.saas.inquiry.pharmacist.server.service.signature.strategy;

import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.DoctorJobTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.PharmacistTypeEnum;
import com.xyy.saas.inquiry.enums.signature.SignatureSealValueTypeEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionUpdateDTO;
import com.xyy.saas.inquiry.mq.prescription.PrescriptionHosAuditCompletedEvent;
import com.xyy.saas.inquiry.mq.prescription.dto.PrescriptionMqCommonMessage;
import com.xyy.saas.inquiry.pharmacist.server.convert.audit.InquiryPharmacistAuditConvert;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.HosPlatPhaFreeAuditMsgDto;
import com.xyy.saas.inquiry.pharmacist.server.mq.producer.PrescriptionHosAuditCompleteProducer;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.strategy.OfflineAuditStrategy;
import com.xyy.saas.inquiry.pharmacist.server.service.signature.dto.SignaturePassingHandleDto;
import jakarta.annotation.Resource;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 医院药师签章
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/03 19:30
 */
@Component("hosPharmacistSign")
@Slf4j
public class HosPharmacistSignaturePassingStrategy extends AbstractInquirySignaturePassingStrategy {


    @Resource
    private OfflineAuditStrategy offlineAuditStrategy;

    @Resource
    private PrescriptionHosAuditCompleteProducer prescriptionHosAuditCompleteProducer;


    @Override
    public void handleSelf(SignaturePassingHandleDto sphDto) {
        // 医院药师 审核完成 抛 MQ
        PrescriptionMqCommonMessage message = PrescriptionMqCommonMessage.builder().prescriptionPref(sphDto.getPrescription().getPref()).prescriptionPdfUrl(sphDto.getPrescription().getPrescriptionPdfUrl()).build();
        prescriptionHosAuditCompleteProducer.sendMessage(PrescriptionHosAuditCompletedEvent.builder().msg(message).build());
    }


    @Override
    public void executeNext(SignaturePassingHandleDto sphDto) {

        InquiryPrescriptionUpdateDTO prescriptionUpdateDTO = InquiryPrescriptionUpdateDTO.builder().id(sphDto.getPrescription().getId()).build();

        Integer sealValueType = sphDto.getSpMessage().getNextField().getSignSealValueType();
        // 医院药师 走 平台药师免签
        if (Objects.equals(sealValueType, SignatureSealValueTypeEnum.PLAT_PHA_FREE_SIGN.getCode())) {

            inquiryPrescriptionApi.updateInquiryPrescription(prescriptionUpdateDTO.setAuditorType(AuditorTypeEnum.PLATFORM_PHARMACIST.getCode())
                .setExt(sphDto.getPrescription().getExt().setSealValueType(sealValueType)));

            HosPlatPhaFreeAuditMsgDto hosPlatPhaFreeAuditMsgDto = InquiryPharmacistAuditConvert.INSTANCE.convertHosPlatAuditDto(sphDto, AuditorTypeEnum.PLATFORM_PHARMACIST, PharmacistTypeEnum.PLATFORM.getCode(),
                DoctorJobTypeEnum.FULL_TIME.getCode());

            offlineAuditStrategy.autoStrategyFreeAuditPrescription(hosPlatPhaFreeAuditMsgDto);
            return;
        }
        // 医院药师 走 医院药师免签审核
        if (Objects.equals(sealValueType, SignatureSealValueTypeEnum.HOS_PHA_FREE_SIGN.getCode())) {

            inquiryPrescriptionApi.updateInquiryPrescription(prescriptionUpdateDTO.setAuditorType(AuditorTypeEnum.HOSPITAL_PHARMACIST.getCode()));

            HosPlatPhaFreeAuditMsgDto hosPlatPhaFreeAuditMsgDto = InquiryPharmacistAuditConvert.INSTANCE.convertHosPlatAuditDto(sphDto, AuditorTypeEnum.HOSPITAL_PHARMACIST, PharmacistTypeEnum.HOSPITAL.getCode(),
                null);

            offlineAuditStrategy.autoStrategyFreeAuditPrescription(hosPlatPhaFreeAuditMsgDto);

        }

        // 医院药师 走 平台药师真人审方
        if (Objects.equals(sealValueType, SignatureSealValueTypeEnum.PLAT_PHA_SIGN.getCode())) {
            /**
             * 1.推送平台药师审方池子 - 处方审方人类型是平台药师 - 处方扩展字段 + 医院药师标识
             * 2.真·平台药师领方时,如果处方上存在医院药师且是自己时,跳过,领取下一张,知道领完后给出提示。(可审核数量仍然可见)
             */

        }

        // 其他.. 推医院药师审方池  or 对接第三方医院系统推单

    }
}
