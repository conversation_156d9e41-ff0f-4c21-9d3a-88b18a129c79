package com.xyy.saas.inquiry.pharmacist.server.convert.audit;

import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.enums.prescription.PrescriptionAuditStatusEnum;
import com.xyy.saas.inquiry.enums.signature.SignatureStatusEnum;
import com.xyy.saas.inquiry.pharmacist.api.audit.dto.InquiryPrescriptionAuditDto;
import com.xyy.saas.inquiry.pharmacist.api.audit.dto.InquiryPrescriptionAuditPageReqDto;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditPageReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditSaveReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditVO;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.audit.InquiryPrescriptionAuditDO;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.HosPlatPhaFreeAuditMsgDto;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.dto.InquiryPharmacistPrescriptionDTO;
import java.time.LocalDateTime;
import java.util.List;
import com.xyy.saas.inquiry.pharmacist.server.service.signature.dto.SignaturePassingHandleDto;
import com.xyy.saas.inquiry.util.HttpHeaderUtil;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author:chenxiaoyi
 * @Date:2024/10/12 17:17
 */
@Mapper
public interface InquiryPharmacistAuditConvert {

    InquiryPharmacistAuditConvert INSTANCE = Mappers.getMapper(InquiryPharmacistAuditConvert.class);

    default InquiryPrescriptionAuditSaveReqVO convertCreateAuditRecord(InquiryPharmacistPrescriptionDTO ppDto, Long userId, String userName, SignatureStatusEnum signatureStatusEnum) {
        return InquiryPrescriptionAuditSaveReqVO.builder()
            .pref(ppDto.getPref())
            .tenantId(ppDto.getTenantId())
            .auditStatus(PrescriptionAuditStatusEnum.PENDING.getCode())
            .auditorType(ppDto.getAuditorType())
            .auditorId(userId)
            .auditorName(userName)
            .auditApprovalType(ppDto.getInquiryWayType())
            .auditorReceiveTime(LocalDateTime.now())
            .auditorApprovalTime(ppDto.getAuditPrescriptionTime())
            .signatureStatus(signatureStatusEnum.getStatusCode())
            .clientChannelType(HttpHeaderUtil.getClientChannelType())
            .build();

    }

    default InquiryPrescriptionAuditSaveReqVO convertCreateAuditRejectRecord(InquiryPharmacistPrescriptionDTO ppDto, AdminUserRespDTO user, InquiryPrescriptionAuditVO auditVO) {
        return InquiryPrescriptionAuditSaveReqVO.builder()
            .pref(ppDto.getPref())
            .auditStatus(PrescriptionAuditStatusEnum.REJECTED.getCode())
            .auditorId(user.getId())
            .auditorName(user.getNickname())
            .auditorRejectedReason(auditVO.getAuditorRejectedReason())
            .auditApprovalType(ppDto.getInquiryWayType())
            .auditorReceiveTime(LocalDateTime.now())
            .auditorApprovalTime(LocalDateTime.now())
            .signatureStatus(SignatureStatusEnum.UNKNOWN.getStatusCode())
            .clientOsType(auditVO.getClientOsType())
            .clientChannelType(HttpHeaderUtil.getClientChannelType())
            .build();

    }

    InquiryPrescriptionAuditDO convertDo(InquiryPrescriptionAuditSaveReqVO createReqVO);

    default HosPlatPhaFreeAuditMsgDto convertHosPlatAuditDto(SignaturePassingHandleDto sphDto, AuditorTypeEnum auditorTypeEnum, Integer pharmacistType, Integer doctorJobType) {
        return HosPlatPhaFreeAuditMsgDto.builder()
            .prescription(sphDto.getPrescription())
            .platformConfigId(sphDto.getSpMessage().getPlatformConfigId())
            .auditorType(auditorTypeEnum)
            .pharmacistType(pharmacistType)
            .doctorJobType(doctorJobType)
            .build();
    }

    InquiryPrescriptionAuditPageReqVO convertReqDTO2ReqVO(InquiryPrescriptionAuditPageReqDto InquiryPrescriptionAuditPageReqDto);

    List<InquiryPrescriptionAuditDto> convertDOList2DTOList(List<InquiryPrescriptionAuditDO> inquiryPrescriptionAuditDOList);

    InquiryPrescriptionAuditDto convertDO2DTO(InquiryPrescriptionAuditDO inquiryPrescriptionAuditDO);

    default InquiryPrescriptionAuditPageReqVO convertAuditPageReqVO(String pref , AuditorTypeEnum auditorTypeEnum){
        InquiryPrescriptionAuditPageReqVO reqVO = new InquiryPrescriptionAuditPageReqVO();
        reqVO.setPref( pref);
        reqVO.setAuditorType(auditorTypeEnum.getCode());
        return reqVO;
     }
}
