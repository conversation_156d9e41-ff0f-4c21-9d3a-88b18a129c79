package com.xyy.saas.inquiry.pharmacist.server.service.signature.strategy;

import cn.hutool.core.util.ObjectUtil;
import com.xyy.saas.inquiry.enums.prescription.PrescriptionAuditStatusEnum;
import com.xyy.saas.inquiry.enums.signature.SignatureStatusEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.InquiryDoctorApi;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.mq.prescription.DoctorSignaturePostPassingEvent;
import com.xyy.saas.inquiry.mq.prescription.DoctorSignaturePostPassingProducer;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditSaveReqVO;
import com.xyy.saas.inquiry.pharmacist.server.convert.signature.InquiryPharmacistSignatureConvert;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.InquiryPrescriptionAuditService;
import com.xyy.saas.inquiry.pharmacist.server.service.signature.dto.SignaturePassingHandleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

// 新增MQ相关导入
import com.xyy.saas.inquiry.mq.prescription.dto.PrescriptionMqCommonMessage;
import jakarta.annotation.Resource;

/**
 * 医师签章
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/03 19:30
 */
@Component("doctorSign")
@Slf4j
public class DoctorSignaturePassingStrategy extends AbstractInquirySignaturePassingStrategy {

    @DubboReference
    private InquiryDoctorApi inquiryDoctorApi;

    @Autowired
    protected InquiryPrescriptionAuditService inquiryPrescriptionAuditService;



    @Resource
    private DoctorSignaturePostPassingProducer doctorSignaturePostPassingProducer;

    /**
     * 1.修改问诊状态 2.处方回传saas
     *
     * @param sphDto
     */
    @Override
    public void handleSelf(SignaturePassingHandleDto sphDto) {
        if (ObjectUtil.isNull(sphDto.getSpMessage()) || ObjectUtil.isNull(sphDto.getPrescription())) {
            return;
        }
        // 医生回调补审核记录
        InquiryPrescriptionAuditSaveReqVO auditSaveReqVO = InquiryPharmacistSignatureConvert.INSTANCE.convertSaveAuditRecord(sphDto, PrescriptionAuditStatusEnum.APPROVED, SignatureStatusEnum.SIGNED);
        InquiryRecordDto inquiryRecord = inquiryApi.getInquiryRecord(sphDto.getPrescription().getInquiryPref());
        if (inquiryRecord != null) {
            InquiryDoctorDto doctor = inquiryDoctorApi.getInquiryDoctorByDoctorPref(inquiryRecord.getDoctorPref());
            InquiryPharmacistSignatureConvert.INSTANCE.fillRecord2AuditVO(auditSaveReqVO, sphDto.getPrescription(), inquiryRecord, doctor);
        }
        inquiryPrescriptionAuditService.createPrescriptionAudit(auditSaveReqVO);

        // 开具处方后置处理 如划价等
        inquiryPrescriptionApi.postProcessIssuesPrescription(sphDto.getPrescription().getPref());

        // IM通知商家处方开具
        inquiryPharmacistImService.sendPrescriptionDrawnFinish(sphDto.getPrescription().getInquiryPref());

        //发送医生签章后置处理MQ
        sendDoctorSignaturePostPassingMQ(sphDto);

    }

    private void sendDoctorSignaturePostPassingMQ(SignaturePassingHandleDto sphDto) {
        try {
            // 构建MQ消息体
            PrescriptionMqCommonMessage message = PrescriptionMqCommonMessage.builder()
                .prescriptionPref(sphDto.getPrescription().getPref())
                .prescriptionImgUrl(sphDto.getPrescription().getPrescriptionImgUrl())
                .prescriptionPdfUrl(sphDto.getPrescription().getPrescriptionPdfUrl())
                .build();

            // 构建MQ事件
            DoctorSignaturePostPassingEvent event = DoctorSignaturePostPassingEvent.builder()
                .msg(message)
                .build();

            doctorSignaturePostPassingProducer.sendMessage(event);
            log.info("【sendDoctorSignaturePostPassingMQ】MQ消息发送成功，处方单号：{}", sphDto.getPrescription().getPref());
        }catch (Exception e) {
            log.error("【sendDoctorSignaturePostPassingMQ】MQ消息发送失败，处方单号：{}，异常信息：{}",
                sphDto.getPrescription().getPref(), e.getMessage(), e);
        }
    }


    @Override
    public void executeNext(SignaturePassingHandleDto sphDto) {
        // 医师签章目前是第一级节点，没有业务的下一级是此service
    }

}
