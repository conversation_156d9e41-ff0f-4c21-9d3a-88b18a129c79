package com.xyy.saas.inquiry.pharmacist.server.dal.redis.audit;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.xyy.saas.inquiry.enums.doctor.PharmacistQualificationEnum;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.enums.system.EnvTagEnum;
import com.xyy.saas.inquiry.pharmacist.server.dal.redis.RedisKeyConstants;
import com.xyy.saas.inquiry.util.LocalDateUtil;
import jakarta.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import kotlin.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

/**
 * @Author:chenxiaoyi
 * @Date:2024/12/06 14:51
 */
@Slf4j
@Component
public class PharmacistAuditRedisService {

    // 避免使用 RedisTemplate 因为value序列化的问题,导致存入的字符串 比较结果异常,兼容使用 StringRedisTemplate
    @Resource
    protected StringRedisTemplate stringRedisTemplate;

    @Resource
    protected RedisTemplate<String, Object> redisTemplate;


    /**
     * 默认超时时间 30  + 时间单位 分钟
     */
    private static final Integer DEFAULT_TIMEOUT = 30;
    private static final TimeUnit TIMEUNIT = TimeUnit.MINUTES;

    /**
     * 药师领方预占单获取
     *
     * @param pharmacistId 药师guid
     * @return <处方pref,审核id>
     */
    public Pair<String, Long> prescriptionAuditReceiveGet(Long pharmacistId) {
        Object value = stringRedisTemplate.opsForValue().get(RedisKeyConstants.PRESCRIPTION_AUDIT_RECEIVE + pharmacistId);
        if (value == null) {
            return null;
        }
        String[] split = StringUtils.split(value.toString(), ":");
        return new Pair<>(split[0], NumberUtil.parseLong(split[1]));

    }

    /**
     * 药师领方预占单设置
     *
     * @param pharmacistId  药师guid
     * @param pref          处方pref
     * @param auditRecordId 审核id
     */
    public void prescriptionAuditReceiveSet(Long pharmacistId, String pref, Long auditRecordId) {
        stringRedisTemplate.opsForValue().set(RedisKeyConstants.PRESCRIPTION_AUDIT_RECEIVE + pharmacistId, pref + ":" + auditRecordId, DEFAULT_TIMEOUT, TIMEUNIT);
    }

    /**
     * 药师领方预占单删除
     *
     * @param pharmacistId 药师guid
     */
    public void prescriptionAuditReceiveDel(Long pharmacistId) {
        stringRedisTemplate.delete(RedisKeyConstants.PRESCRIPTION_AUDIT_RECEIVE + pharmacistId);
    }

    // ======================== 药师审方池排除 key ============================

    /**
     * 药师审方池排除key 设置
     *
     * @param pharmacistId 药师id
     * @param pref         处方号
     * @param time         时间
     */
    public void prescriptionAuditReviewExcludeSet(Long pharmacistId, String pref, LocalDateTime time) {
        stringRedisTemplate.opsForZSet().addIfAbsent(RedisKeyConstants.getPrescriptionReviewExcludeKey(pharmacistId), pref, LocalDateUtil.convertScore(time));
    }

    /**
     * 药师审方池排除key 获取数量  时间范围  now < time <  now + 1天
     *
     * @param pharmacistId
     * @param time         最小时间
     */
    public Long prescriptionAuditReviewExcludeCount(Long pharmacistId, LocalDateTime time) {
        return Optional.ofNullable(stringRedisTemplate.opsForZSet().count(RedisKeyConstants.getPrescriptionReviewExcludeKey(pharmacistId), LocalDateUtil.convertScore(time), LocalDateUtil.convertScore(LocalDateTime.now().plusDays(1))))
            .orElse(0L);
    }

    /**
     * 药师审方池排除key 设置
     *
     * @param pharmacistId 药师id
     * @param pref         处方号
     */
    public void prescriptionAuditReviewExcludeRemove(Long pharmacistId, String pref) {
        stringRedisTemplate.opsForZSet().remove(RedisKeyConstants.getPrescriptionReviewExcludeKey(pharmacistId), pref);
    }

    /**
     * 删除分数小于当前时间的元素(已过期)
     *
     * @param pharmacistId 药师id
     */
    public void prescriptionAuditReviewExcludeCleanExpired(Long pharmacistId) {
        stringRedisTemplate.opsForZSet().removeRangeByScore(RedisKeyConstants.getPrescriptionReviewExcludeKey(pharmacistId), 0, LocalDateUtil.convertScore(LocalDateTime.now()));
    }

    // ============================== 门店药师审方池 =============================

    /**
     * 处方 门店药师审方池 推入
     *
     * @param envTag       环境
     * @param medicineType 药品类型
     * @param pref         处方号
     */
    public void prescriptionWaitingReviewDrugstorePoolPush(EnvTagEnum envTag, MedicineTypeEnum medicineType, Long tenantId, String pref, LocalDateTime outPrescriptionTime) {
        stringRedisTemplate.opsForZSet().add(RedisKeyConstants.getPrescriptionWaitingReviewDrugstorePoolKey(envTag, medicineType, tenantId), pref, LocalDateUtil.convertScore(outPrescriptionTime));
    }

    /**
     * 处方 门店药师审方池 移除
     */
    public void prescriptionWaitingReviewDrugstorePoolRemove(EnvTagEnum envTag, MedicineTypeEnum medicineType, Long tenantId, String... pref) {
        stringRedisTemplate.opsForZSet().remove(RedisKeyConstants.getPrescriptionWaitingReviewDrugstorePoolKey(envTag, medicineType, tenantId), pref);
    }

    // ============================== 门店药师审方池 =============================

    // ============================== 连锁药师审方池 =============================

    /**
     * 处方 连锁药师审方池 推入
     *
     * @param envTag       环境
     * @param medicineType 药品类型
     * @param areaCode     处方地区
     * @param pref         处方号
     */
    public void prescriptionWaitingReviewChainPoolPush(EnvTagEnum envTag, MedicineTypeEnum medicineType, Long tenantId, String pref, LocalDateTime outPrescriptionTime) {
        stringRedisTemplate.opsForZSet().add(RedisKeyConstants.getPrescriptionWaitingReviewChainPoolKey(envTag, medicineType, tenantId), pref, LocalDateUtil.convertScore(outPrescriptionTime));
    }

    /**
     * 处方 连锁药师审方池 移除
     */
    public void prescriptionWaitingReviewChainPoolRemove(EnvTagEnum byEnv, MedicineTypeEnum medicineTypeEnum, Long headTenantId, String... pref) {
        stringRedisTemplate.opsForZSet().remove(RedisKeyConstants.getPrescriptionWaitingReviewChainPoolKey(byEnv, medicineTypeEnum, headTenantId), pref);
    }
    // ============================== 连锁总部药师审方池 =============================

// ============================== 平台药师审方池 =============================

    /**
     * 处方 平台药师审方池 推入
     *
     * @param envTag              环境
     * @param medicineType        药品类型
     * @param areaCode            处方地区
     * @param pref                处方号
     * @param outPrescriptionTime 开方时间
     */
    public void prescriptionWaitingReviewPlatformPoolPush(EnvTagEnum envTag, MedicineTypeEnum medicineType, String areaCode, String pref, LocalDateTime outPrescriptionTime) {
        stringRedisTemplate.opsForZSet().add(RedisKeyConstants.getPrescriptionWaitingReviewPlatformPoolKey(envTag, medicineType, areaCode), pref, LocalDateUtil.convertScore(outPrescriptionTime));
    }

    /**
     * 处方 平台药师审方池 移除
     *
     * @param envTag       环境
     * @param medicineType 药品类型
     * @param areaCode     处方地区
     * @param pref         处方号
     */
    public void prescriptionWaitingReviewPlatformPoolRemove(EnvTagEnum envTag, MedicineTypeEnum medicineType, String areaCode, String... pref) {
        stringRedisTemplate.opsForZSet().remove(RedisKeyConstants.getPrescriptionWaitingReviewPlatformPoolKey(envTag, medicineType, areaCode), pref);
    }

    /**
     * 处方 平台药师远程审方池 推入
     *
     * @param envTag              环境
     * @param medicineType        药品类型
     * @param areaCode            处方地区
     * @param pref                处方号
     * @param outPrescriptionTime 开方时间
     */
    public void prescriptionWaitingReviewRemotePlatformPoolPush(EnvTagEnum envTag, MedicineTypeEnum medicineType, String areaCode, String pref, LocalDateTime outPrescriptionTime) {
        stringRedisTemplate.opsForZSet().add(RedisKeyConstants.getPrescriptionWaitingReviewRemotePlatformPoolKey(envTag, medicineType, areaCode), pref, LocalDateUtil.convertScore(outPrescriptionTime));
    }

    /**
     * 处方 平台药师远程审方池 移除
     *
     * @param envTag       环境
     * @param medicineType 药品类型
     * @param areaCode     处方地区
     * @param pref         处方号
     */
    public void prescriptionWaitingReviewRemotePlatformPoolRemove(EnvTagEnum envTag, MedicineTypeEnum medicineType, String areaCode, String... pref) {
        stringRedisTemplate.opsForZSet().remove(RedisKeyConstants.getPrescriptionWaitingReviewRemotePlatformPoolKey(envTag, medicineType, areaCode), pref);
    }

    /**
     * 处方 平台药师审方池 获取
     *
     * @param envTag                  环境
     * @param pharmacistQualification 药师资质
     * @param areaCode                处方地区
     * @return 审方池数量
     */
    public Long prescriptionWaitingReviewPlatformPoolCount(EnvTagEnum envTag, PharmacistQualificationEnum pharmacistQualification, String areaCode) {
        // 平台要是 查看自己执业地区 + 全国的
        List<String> areaCodes = Stream.of(areaCode, "").distinct().toList();
        // 平台审方池
        List<String> poolKeys =
            areaCodes.stream().flatMap(a ->
                PharmacistQualificationEnum.convertMedicineTypeFromCode(pharmacistQualification.getCode()).stream().map(
                    m -> RedisKeyConstants.getPrescriptionWaitingReviewPlatformPoolKey(envTag, MedicineTypeEnum.fromCode(m), a))).collect(Collectors.toList());

        // 平台远程审方池
        List<String> remotePoolKeys =
            areaCodes.stream().flatMap(a ->
                PharmacistQualificationEnum.convertMedicineTypeFromCode(pharmacistQualification.getCode()).stream().map(
                    m -> RedisKeyConstants.getPrescriptionWaitingReviewRemotePlatformPoolKey(envTag, MedicineTypeEnum.fromCode(m), a))).toList();
        poolKeys.addAll(remotePoolKeys);

        return prescriptionWaitingReviewPoolCount(poolKeys);
    }

    /**
     * 平台审方池弹出一个
     *
     * @param envTag                  环境
     * @param pharmacistQualification 药师资质
     * @param areaCode                处方地区
     */
    public String prescriptionWaitingReviewPlatformPoolPopMin(EnvTagEnum envTag, PharmacistQualificationEnum pharmacistQualification, String areaCode) {
        // 先领取自己执业地区的
        // 平台
        List<String> poolKeys = PharmacistQualificationEnum.convertMedicineTypeFromCode(pharmacistQualification.getCode())
            .stream().map(m -> RedisKeyConstants.getPrescriptionWaitingReviewPlatformPoolKey(envTag, MedicineTypeEnum.fromCode(m), areaCode)).collect(Collectors.toList());
        // 远程
        List<String> remotePoolKeys = PharmacistQualificationEnum.convertMedicineTypeFromCode(pharmacistQualification.getCode())
            .stream().map(m -> RedisKeyConstants.getPrescriptionWaitingReviewRemotePlatformPoolKey(envTag, MedicineTypeEnum.fromCode(m), areaCode)).toList();

        poolKeys.addAll(remotePoolKeys);

        String pref = prescriptionWaitingReviewPoolPopMin(poolKeys);

        // 如果有自己地区的 且 审核完了 ,领取全国的
        if (StringUtils.isNotBlank(areaCode) && StringUtils.isBlank(pref)) {
            // 平台
            List<String> globalPoolKeys = PharmacistQualificationEnum.convertMedicineTypeFromCode(pharmacistQualification.getCode())
                .stream().map(m -> RedisKeyConstants.getPrescriptionWaitingReviewPlatformPoolKey(envTag, MedicineTypeEnum.fromCode(m), "")).collect(Collectors.toList());
            // 远程
            List<String> globalRemotePoolKeys = PharmacistQualificationEnum.convertMedicineTypeFromCode(pharmacistQualification.getCode())
                .stream().map(m -> RedisKeyConstants.getPrescriptionWaitingReviewRemotePlatformPoolKey(envTag, MedicineTypeEnum.fromCode(m), "")).toList();

            globalPoolKeys.addAll(globalRemotePoolKeys);

            pref = prescriptionWaitingReviewPoolPopMin(globalPoolKeys);
        }
        return pref;
    }

// ============================== 平台药师审方池 =============================

// ============================== 处方审方池 通用逻辑 =============================


    /**
     * 处方审方池 lua脚本 - 当前处方审方池中获取最早时间的一个处方号 参数说明：传入keys:当前药师所在的审方池组装的key 第一轮：寻找全局最小元素 第二轮：移除元素并返回
     */
    private static final DefaultRedisScript<String> script = new DefaultRedisScript<>(
        "local min_score = nil\n"
            + "local min_key = nil\n"
            + "local min_member = nil\n"
            + "\n"
            + "for _, key in ipairs(KEYS) do\n"
            + "    local res = redis.call('ZRANGE', key, 0, 0, 'WITHSCORES')\n"
            + "    \n"
            + "    if #res > 0 then\n"
            + "        local current_score = tonumber(res[2])\n"
            + "        if not min_score or current_score < min_score then\n"
            + "            min_score = current_score\n"
            + "            min_key = key\n"
            + "            min_member = res[1]\n"
            + "        end\n"
            + "    end\n"
            + "end\n"
            + "\n"
            + "if not min_key then\n"
            + "    return nil\n"
            + "end\n"
            + "\n"
            + "local removed = redis.call('ZREM', min_key, min_member)\n"
            + "if removed == 0 then\n"
            + "    return nil\n"
            + "end\n"
            + "\n"
            + "return min_member",
        String.class
    );

    /**
     * 药师审方池 获取一个处方号
     * <p> 从几个池子中排除某些元素，并找到排名最早的一个元素 </p>
     */
    private static final DefaultRedisScript<String> script_v2 = new DefaultRedisScript<>(
        """
            -- KEYS: 一个或多个 ZSET 的 key
            -- ARGV[1]: 排除集合的 key
            
            local exclude_key = ARGV[1] or ''  -- 处理 ARGV[1] 为 nil 的情况
            local batch_size = 100
            local min_score, min_key, min_member
            local excluded_set = {}
            
            -- 1. 仅当 exclude_key 非空且存在时，构建排除集合
            if string.len(exclude_key) > 0 then
                if redis.call('TYPE', exclude_key)['ok'] == 'zset' then  -- 确保类型为 ZSET
                    local excluded_members = redis.call('ZRANGE', exclude_key, 0, -1)
                    for _, member in ipairs(excluded_members) do
                        excluded_set[member] = true
                    end
                end
            end
            
             -- 2. 分批遍历所有 ZSET 
            for _, key in ipairs(KEYS) do
                local member_count = redis.call('ZCARD', key)
                local cursor = 0
            
                 -- 2.1 找到最小时间处方
                while cursor < member_count do
                    local members = redis.call('ZRANGE', key, cursor, cursor + batch_size -1, 'WITHSCORES')
                    if #members == 0 then break end
            
                    for i = 1, #members, 2 do
                        local member = members[i]
                        local score = tonumber(members[i+1])
            
                        -- 2.2  且 不再排除的集合中
                        if not excluded_set[member] then
                            if not min_score or score < min_score then
                                min_score, min_key, min_member = score, key, member
                            end
                        end
                    end
            
                    cursor = cursor + batch_size
                end
            end
            
            -- 3  移除最小的元素
            if min_member then
                if redis.call('ZREM', min_key, min_member) == 1 then
                    return min_member
                end
            end
            return nil
            """,
        String.class
    );


    /**
     * 处方 审方池 待审数量
     *
     * @param poolKeys 池子key - eg:  (门店审方池 + 连锁总部药师审方池) || 平台审方池
     * @return 待审数量
     */
    public Long prescriptionWaitingReviewPoolCount(List<String> poolKeys) {

        if (CollUtil.isEmpty(poolKeys)) {
            return 0L;
        }

        // 使用Pipeline批量获取所有Key的处方数量
        List<Object> counts = stringRedisTemplate.executePipelined(
            (RedisCallback<Object>) connection -> {
                for (String key : poolKeys) {
                    connection.zCard(key.getBytes(StandardCharsets.UTF_8));
                }
                return null;
            }
        );

        return counts.stream().filter(Objects::nonNull).mapToLong(c -> (Long) c).sum();
    }

    /**
     * 处方 审方池 弹出一个时间最早的
     *
     * @param poolKeys 池子key - eg: (门店审方池 + 连锁总部药师审方池) || 平台审方池
     * @return
     */
    public String prescriptionWaitingReviewPoolPopMin(List<String> poolKeys) {

        if (CollUtil.isEmpty(poolKeys)) {
            return null;
        }
        // 1. 移除过期排除池
        // prescriptionAuditReviewExcludeCleanExpired(pharmacistId);
        // String excludeKeys = RedisKeyConstants.getPrescriptionReviewExcludeKey(pharmacistId);

        final String res = stringRedisTemplate.execute(
            script,
            poolKeys
        );
        return StringUtils.removeEnd(StringUtils.removeStart(res, "\""), "\"");

    }

}
