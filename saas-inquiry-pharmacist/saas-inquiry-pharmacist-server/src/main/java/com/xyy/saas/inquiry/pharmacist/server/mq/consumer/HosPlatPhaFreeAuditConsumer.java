package com.xyy.saas.inquiry.pharmacist.server.mq.consumer;

import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.HosPlatPhaFreeAuditEvent;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.HosPlatPhaFreeAuditMsgDto;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.strategy.OfflineAuditStrategy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 医院平台药师免签审核Consumer
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/03 16:29
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_pharmacist_server_mq_consumer_HosPlatPhaFreeAuditConsumer",
    topic = HosPlatPhaFreeAuditEvent.TOPIC)
public class HosPlatPhaFreeAuditConsumer {


    @Resource
    private OfflineAuditStrategy offlineAuditStrategy;

    @EventBusListener
    public void hosPlatPhaFreeAuditConsumer(HosPlatPhaFreeAuditEvent hosPlatPhaFreeAuditEvent) {
        HosPlatPhaFreeAuditMsgDto msgDto = hosPlatPhaFreeAuditEvent.getMsg();
        if (msgDto.getPrescription() == null || msgDto.getConsumerCount() > 60 * 24 * 3) {
            log.warn("医院平台药师免签 - 处方信息为空或超过3天,不再处理审方");
            return;
        }

        TenantUtils.execute(msgDto.getPrescription().getTenantId(), () -> offlineAuditStrategy.autoStrategyFreeAuditPrescription(msgDto));
    }
}
