package com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo;

import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.framework.common.validation.Mobile;
import com.xyy.saas.inquiry.enums.doctor.DoctorJobTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.DrawnSignEnum;
import com.xyy.saas.inquiry.enums.doctor.PharmacistNatureEnum;
import com.xyy.saas.inquiry.enums.doctor.PharmacistQualificationEnum;
import com.xyy.saas.inquiry.enums.doctor.PharmacistTypeEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.identification.InquiryProfessionIdentificationDto;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryHospitalDeptDoctorDto;
import com.xyy.saas.inquiry.pojo.parmacist.PharmacistExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

@Schema(description = "管理后台 - 药师信息新增/修改 Request VO")
@Data
@Accessors(chain = true)
public class InquiryPharmacistSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "23483")
    private Long id;

    /**
     * 药师编码
     */
    private String pref;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19857")
    private Long userId;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "姓名不能为空")
    @Length(max = 18, min = 1, message = "姓名长度为1-18位")
    private String name;

    @Schema(description = "性别 1男 2女", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "性别 1男 2女不能为空")
    private Integer sex;

    @Schema(description = "身份证号码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "身份证号码不能为空")
    @Length(max = 18, min = 15, message = "身份证号码长度为15-18位")
    private String idCard;

    @Schema(description = "电话号码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "电话号码不能为空")
    @Mobile
    private String mobile;
    //
    @Schema(description = "审核状态 0、待审核  1、审核通过  2、审核驳回", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer auditStatus;
//

    @Schema(description = "证件照地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "证件照地址不能为空")
    private String photo;

    @Schema(description = "个人简介", requiredMode = Schema.RequiredMode.REQUIRED)
    // @NotEmpty(message = "个人简介不能为空")
    @Length(max = 200, message = "个人简介长度为1-200")
    private String biography;

    @Schema(description = "药师执业资格,中药或西药", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "药师执业资格,中药或西药不能为空")
    @InEnum(value = PharmacistQualificationEnum.class)
    private Integer qualification;

    @Schema(description = "药师类型 平台药师 / 门店药师 / 医院药师", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
//    @NotNull(message = "药师类型不能为空")
    @InEnum(value = PharmacistTypeEnum.class)
    private Integer pharmacistType;


    @Schema(description = "药师性质 ", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @InEnum(value = PharmacistNatureEnum.class)
    private Integer pharmacistNature;

    @Schema(description = "降级绘制标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @InEnum(value = DrawnSignEnum.class)
    private Integer drawnSign;

    @Schema(description = "毕业学校", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "毕业学校不能为空")
    @Length(max = 50, min = 1, message = "毕业学校长度为1-50")
    private String school;

    /**
     * 平台药师才有
     */
    @Schema(description = "执业省份")
    private String provinceCode;

    @Schema(description = "药师工作类型 全职/兼职", example = "2")
    @InEnum(value = DoctorJobTypeEnum.class)
    private Integer jobType;


    @Schema(description = "民族", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer nationCode;

    @Schema(description = "学历", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer formalLevel;

    @Schema(description = "通信地址")
    private String address;

    @Schema(description = "备注")
    @Length(max = 500, message = "备注长度为0-500")
    private String remark;

    @Schema(description = "资质信息", requiredMode = Schema.RequiredMode.REQUIRED)
    @Valid
    private List<InquiryProfessionIdentificationDto> professionIdentifications;

    @Schema(description = "医院科室关系信息")
    private List<InquiryHospitalDeptDoctorDto> hospitalDeptDoctorRelations;

    @Schema(description = "环境标志：prod-真实数据；test-测试数据；show-线上演示数据", requiredMode = Schema.RequiredMode.REQUIRED)
    private String envTag;
    /**
     * 旧系统唯一标识
     */
    private String guid;

    /**
     * 密码
     */
    private String password;

    /**
     * 药师扩展信息
     */
    private PharmacistExtDto ext;
}