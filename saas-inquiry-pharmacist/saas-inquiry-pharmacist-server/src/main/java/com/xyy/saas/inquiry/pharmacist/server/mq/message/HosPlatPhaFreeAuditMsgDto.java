package com.xyy.saas.inquiry.pharmacist.server.mq.message;

import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.dto.InquiryPharmacistPrescriptionDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 医院平台药师免签审核事件msg
 *
 * <AUTHOR>
 */
@Builder
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class HosPlatPhaFreeAuditMsgDto implements Serializable {

    /**
     * 处方数据
     */
    private InquiryPharmacistPrescriptionDTO prescription;

    /**
     * 签章平台应用配置id
     */
    private Integer platformConfigId;

    /**
     * 当前审核人类型
     */
    private AuditorTypeEnum auditorType;

    /**
     * 药师类型
     */
    private Integer pharmacistType;

    /**
     * 药师性质
     */
    private Integer doctorJobType;


    /**
     * 消费次数
     */
    private int consumerCount = 0;
}
