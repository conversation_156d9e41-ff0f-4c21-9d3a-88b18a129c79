package com.xyy.saas.inquiry.pharmacist.server.api.audit;

import cn.hutool.core.collection.CollUtil;
import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.pharmacist.api.audit.InquiryPrescriptionAuditApi;
import com.xyy.saas.inquiry.pharmacist.api.audit.dto.InquiryPrescriptionAuditDto;
import com.xyy.saas.inquiry.pharmacist.api.audit.dto.InquiryPrescriptionAuditPageReqDto;
import com.xyy.saas.inquiry.pharmacist.api.audit.dto.RemotePrescriptionAuditDto;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditPageReqVO;
import com.xyy.saas.inquiry.pharmacist.server.convert.audit.InquiryPharmacistAuditConvert;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.audit.InquiryPrescriptionAuditDO;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.InquiryPrescriptionAuditService;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.InquiryPharmacistService;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;
import java.util.List;
import java.util.Optional;

/**
 * @Author:chenxiaoyi
 * @Date:2024/12/26 11:49
 */
@DubboService
public class InquiryPrescriptionAuditApiImpl implements InquiryPrescriptionAuditApi {


    @Resource
    private InquiryPrescriptionAuditService inquiryPrescriptionAuditService;

    @Resource
    private InquiryPharmacistService inquiryPharmacistService;

    @Override
    public void remotePrescriptionPushAuditPool(RemotePrescriptionAuditDto auditDto) {
        inquiryPrescriptionAuditService.remotePrescriptionOperateAuditPool(auditDto.setAudit(true));
    }

    @Override
    public void remotePrescriptionRemoveAuditPool(RemotePrescriptionAuditDto auditDto) {
        inquiryPrescriptionAuditService.remotePrescriptionOperateAuditPool(auditDto);
    }


    @Override
    public void remotePrescriptionBatchRemoveAuditPool(RemotePrescriptionAuditDto auditDto) {
        inquiryPrescriptionAuditService.remotePrescriptionBatchRemoveAuditPool(auditDto);
    }

    @Override
    public List<InquiryPrescriptionAuditDto> selectListByCondition(InquiryPrescriptionAuditPageReqDto inquiryPrescriptionAuditPageReqDto) {
        InquiryPrescriptionAuditPageReqVO inquiryPrescriptionAuditPageReqVO = InquiryPharmacistAuditConvert.INSTANCE.convertReqDTO2ReqVO(inquiryPrescriptionAuditPageReqDto);
        List<InquiryPrescriptionAuditDO> inquiryPrescriptionAuditDOList = inquiryPrescriptionAuditService.selectListByCondition(inquiryPrescriptionAuditPageReqVO);
        return InquiryPharmacistAuditConvert.INSTANCE.convertDOList2DTOList(inquiryPrescriptionAuditDOList);
    }


    @Override
    public InquiryPrescriptionAuditDto selectByAuditorType(String pref, AuditorTypeEnum auditorType) {
        InquiryPrescriptionAuditPageReqVO auditPageReqVO = new InquiryPrescriptionAuditPageReqVO().setAuditorType(auditorType.getCode()).setPref(pref);
        List<InquiryPrescriptionAuditDO> inquiryPrescriptionAuditDOList = inquiryPrescriptionAuditService.selectListByCondition(auditPageReqVO);
        if (CollUtil.isEmpty(inquiryPrescriptionAuditDOList)) {
            return null;
        }
        InquiryPrescriptionAuditDO auditDO = inquiryPrescriptionAuditDOList.getFirst();
        InquiryPrescriptionAuditDto auditDto = InquiryPharmacistAuditConvert.INSTANCE.convertDO2DTO(auditDO);
        // 填充药师编码
        if (auditDO.getAuditorId() != null) {
            Optional.ofNullable(inquiryPharmacistService.getPharmacistByUserId(auditDO.getAuditorId()))
                .ifPresent(pharmacist -> auditDto.setAuditorPref(pharmacist.getPref()));
        }
        return auditDto;
    }
}
