package com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 处方审核记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class InquiryPrescriptionAuditPageReqVO extends PageParam {

    @Schema(description = "处方编号")
    private String pref;

    @Schema(description = "处方编号集合")
    private List<String> prefList;

    @Schema(description = "审核人类型 1:医生 2:平台药师 3:药店药师", example = "1")
    private Integer auditorType;

    @Schema(description = "审核人类型集合 1:医生 2:平台药师 3:药店药师", example = "1")
    private List<Integer> auditorTypeList;

    @Schema(description = "记录审批级数")
    private Integer auditLevel;

    @Schema(description = "审核状态 1:待审核 2:审核通过 3:审核驳回", example = "2")
    private Integer auditStatus;

    @Schema(description = "审核人(药师)id", example = "28777")
    private Long auditorId;

    @Schema(description = "审核人(药师)名称", example = "芋艿")
    private String auditorName;

    @Schema(description = "审方类型 1:图文 2:视频 3:电话", example = "1")
    private Integer auditApprovalType;

    @Schema(description = "领单时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] auditorReceiveTime;

    @Schema(description = "药师审批时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] auditorApprovalTime;

    @Schema(description = "药师驳回原因", example = "不喜欢")
    private String auditorRejectedReason;

    @Schema(description = "药师签名")
    private String auditorCaSign;

    @Schema(description = "药师签名图片", example = "https://www.iocoder.cn")
    private String auditorSignImgUrl;

    @Schema(description = "签章状态 1:待签章 2:发起签章 3:已签章", example = "1")
    private Integer signatureStatus;

    @Schema(description = "审核人 (药师)发起签名时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] auditorSignatureTime;

    @Schema(description = "审核人 (药师)签名回调时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] auditorCallbackTime;

    @Schema(description = "审方端客户端类型 0、app  1、PC  2、小程序 ", example = "2")
    private Integer clientChannelType;

    @Schema(description = "审核 (药师)端操作系统类型 前端传入", example = "1")
    private String clientOsType;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}