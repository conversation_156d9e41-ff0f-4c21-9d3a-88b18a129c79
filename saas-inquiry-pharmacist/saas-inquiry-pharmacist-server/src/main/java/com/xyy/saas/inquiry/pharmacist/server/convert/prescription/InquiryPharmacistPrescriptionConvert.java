package com.xyy.saas.inquiry.pharmacist.server.convert.prescription;

import com.xyy.saas.inquiry.enums.inquiry.PrescriptionStatusEnum;
import com.xyy.saas.inquiry.enums.prescription.template.PrescriptionTemplateFieldEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionUpdateDTO;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.pharmacist.api.audit.dto.RemotePrescriptionDto;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionReceiveVO;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.dto.InquiryPharmacistPrescriptionDTO;
import com.xyy.saas.inquiry.signature.mq.SignaturePassingMessage;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author:chenxiaoyi
 * @Date:2024/10/12 17:17
 */
@Mapper
public interface InquiryPharmacistPrescriptionConvert {

    InquiryPharmacistPrescriptionConvert INSTANCE = Mappers.getMapper(InquiryPharmacistPrescriptionConvert.class);

    InquiryPharmacistPrescriptionDTO convertDTO(InquiryPrescriptionRespDTO prescription);

    default InquiryPrescriptionUpdateDTO convertSignPassingUpdateDto(SignaturePassingMessage spMessage, InquiryPrescriptionRespDTO prescription) {
        Integer status = PrescriptionTemplateFieldEnum.isAuditEnd(spMessage.getNextField() == null
            ? "" : spMessage.getNextField().getField())
            ? PrescriptionStatusEnum.APPROVAL.getStatusCode()
            : Objects.equals(prescription.getStatus(), PrescriptionStatusEnum.WAITING.getStatusCode())
                ? PrescriptionStatusEnum.WAIT_APPROVAL.getStatusCode() : null;

        prescription.setPrescriptionImgUrl(spMessage.getImgUrl());
        prescription.setPrescriptionPdfUrl(spMessage.getPdfUrl());

        return InquiryPrescriptionUpdateDTO.builder()
            .id(prescription.getId())
            .pref(prescription.getPref())
            .auditLevel(spMessage.getTotalLevel())
            .prescriptionPdfUrl(spMessage.getPdfUrl())
            .prescriptionImgUrl(spMessage.getImgUrl())
            .status(status).build();
    }


    default InquiryPrescriptionReceiveVO convertReceiveVO(InquiryPharmacistPrescriptionDTO prescriptionDTO, InquiryRecordDetailDto inquiryRecordDetail, Long auditRecordId, Integer auditTimeOut) {

        // boolean auditVideo = ClientChannelTypeEnum.isPc(HttpHeaderUtil.getClientChannelType()) && prescriptionDTO.getExt() != null && prescriptionDTO.getExt().isPrescriptionAuditVideo();

        InquiryPrescriptionReceiveVO vo = convertReceiveVO(prescriptionDTO);
        vo.setAuditRecordId(auditRecordId);
        vo.setGestationLactationValue(inquiryRecordDetail.getGestationLactationValue());
        vo.setAllergic(inquiryRecordDetail.getAllergic());
        vo.setLiverKidneyValue(inquiryRecordDetail.getLiverKidneyValue());
        vo.setSlowDisease(inquiryRecordDetail.getSlowDisease());
        // vo.setPrescriptionAuditVideo(auditVideo);
        vo.setAuditTimeOut(auditTimeOut);
        return vo;
    }

    @Mapping(target = "prescriptionId", source = "id")
    InquiryPrescriptionReceiveVO convertReceiveVO(InquiryPharmacistPrescriptionDTO prescriptionDTO);

    InquiryPharmacistPrescriptionDTO convertDTO(RemotePrescriptionDto remotePrescriptionDto);
}
