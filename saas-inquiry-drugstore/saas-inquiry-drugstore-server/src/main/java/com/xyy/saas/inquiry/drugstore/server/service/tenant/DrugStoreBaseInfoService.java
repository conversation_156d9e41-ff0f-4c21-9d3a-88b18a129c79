package com.xyy.saas.inquiry.drugstore.server.service.tenant;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageRelationPageReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageRelationRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantPackageCostDto;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.DrugStorePackageReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.DrugStorePackageRespVO;
import com.xyy.saas.inquiry.drugstore.server.controller.app.drugstore.vo.DrugStoreInquiryPermissionVO;
import com.xyy.saas.inquiry.drugstore.server.controller.app.drugstore.vo.LocationVO;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageEffectiveStatusEnum;

import java.util.List;

/**
 * 门店参数配置 Service 接口
 *
 * <AUTHOR>
 */
public interface DrugStoreBaseInfoService {

    /**
     * 门店经纬度信息设置
     *
     * @param locationVO 经纬度VO
     */
    void saveLocation(LocationVO locationVO);

    /**
     * 获取门店经纬度+离店使用开关
     *
     * @return
     */
    LocationVO getLocation();

    /**
     * 获取门店问诊权限
     *
     * @return 问诊权限VO
     */
    DrugStoreInquiryPermissionVO getDrugStoreInquiryPermission();

    /**
     * 获取门店套餐列表
     *
     * @param reqVO 套餐VO
     * @return
     */
    List<DrugStorePackageRespVO> getDrugStorePackageList(DrugStorePackageReqVO reqVO);

    /**
     * 获取当前门店生效的套餐 - 一条
     *
     * @return 门店套餐信息
     */
    DrugStorePackageRespVO getDrugStoreCurrentEffectivePackage();

    /**
     * 获得门店套餐包信息分页
     *
     * @param reqDto 分页查询
     * @return 门店套餐包信息分页
     */
    PageResult<TenantPackageRelationRespDto> pageDrugStorePackageList(TenantPackageRelationPageReqDto reqDto);

    /**
     * 获取当前门店问诊规则配置
     *
     * @return 规则
     */
    CommonResult<InquiryOptionConfigRespDto> getInquiryRuleConfig();

    /**
     * 获取医生端处方规则
     *
     * @return
     */
    CommonResult<InquiryOptionConfigRespDto> getInquiryRuleConfigDoctor(Long tenantId, Integer prescriptionType);


    List<TenantPackageCostDto> selectUserTenantPackages(Long userId, List<TenantPackageEffectiveStatusEnum> effectiveStatusEnums);

    /**
     * 判断传入的租户id是否可问诊
     *
     * @param tenantId
     * @return
     */
    DrugStoreInquiryPermissionVO tenantCanInquiry(Long tenantId);

    List<TenantPackageRelationRespDto> getDrugStorePackageListByTenantId(DrugStorePackageReqVO reqVO);
}