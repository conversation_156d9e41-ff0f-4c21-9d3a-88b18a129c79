package com.xyy.saas.inquiry.drugstore.server.service.tenant;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_DISABLE;
import static com.xyy.saas.inquiry.drugstore.enums.ErrorCodeConstants.TENANT_PACKAGE_INQUIRY_NO_ERROR;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.system.api.dict.DictDataApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantPackageApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantUserRelationApi;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageRelationPageReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageRelationRespDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserRelationDto;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.alibaba.fastjson.JSON;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.xyy.saas.inquiry.constant.TenantPackageConstant;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantPackageCostDto;
import com.xyy.saas.inquiry.drugstore.api.user.UserApi;
import com.xyy.saas.inquiry.drugstore.enums.AppHomeCoreMeunEnum;
import com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo.TenantPackageCostReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.DrugStorePackageReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.DrugStorePackageRespVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.TenantParamConfigSaveReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.app.drugstore.vo.DrugStoreInquiryPermissionVO;
import com.xyy.saas.inquiry.drugstore.server.controller.app.drugstore.vo.LocationVO;
import com.xyy.saas.inquiry.drugstore.server.convert.menu.MenuConvert;
import com.xyy.saas.inquiry.drugstore.server.convert.tennat.DrugStoreBaseInfoConvert;
import com.xyy.saas.inquiry.drugstore.server.convert.tennat.TenantPackageCostConvert;
import com.xyy.saas.inquiry.drugstore.server.convert.tennat.TenantParamConfigConvert;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantPackageCostDO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantParamConfigDO;
import com.xyy.saas.inquiry.drugstore.server.service.option.InquiryOptionConfigService;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageEffectiveStatusEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem;
import com.xyy.saas.transmitter.api.transmission.TransmissionConfigApi;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 门店基本信息service
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DrugStoreBaseInfoServiceImpl implements DrugStoreBaseInfoService {

    @Resource
    private TenantParamConfigService tenantParamConfigService;

    @Resource
    private TenantPackageCostService tenantPackageCostService;

    @Resource
    private InquiryOptionConfigService inquiryOptionConfigService;

    @Autowired
    private TenantApi tenantApi;

    @Autowired
    private TenantPackageApi tenantPackageApi;

    @Autowired
    private TenantUserRelationApi tenantUserRelationApi;

    @Resource
    private UserApi userApi;

    @Resource
    private DictDataApi dictDataApi;

    @DubboReference
    private TransmissionConfigApi transmissionConfigApi;


    @Override
    public void saveLocation(LocationVO locationVO) {
        TenantParamConfigSaveReqVO paramConfigSaveReqVO = TenantParamConfigConvert.INSTANCE.convert(TenantParamConfigTypeEnum.LL, locationVO.getLongitude() + "," + locationVO.getLatitude());
        tenantParamConfigService.saveOrUpdateTenantParamConfig(paramConfigSaveReqVO);
    }

    @Override
    public LocationVO getLocation() {
        LocationVO locationVO = new LocationVO();
        // 获取经纬度
        TenantParamConfigDO llConfigDO = tenantParamConfigService.getTenantParamConfig(TenantParamConfigTypeEnum.LL);
        if (llConfigDO != null && StringUtils.isNotBlank(llConfigDO.getParamValue())) {
            locationVO.setLongitude(llConfigDO.getParamValue().split(",")[0]).setLatitude(llConfigDO.getParamValue().split(",")[1]);
        }
        // 获取离店限制开关
        TenantParamConfigDO distanceLimitSwitch = tenantParamConfigService.getTenantParamConfig(TenantParamConfigTypeEnum.INQUIRY_DISTANCE_LIMIT_SWITCH);
        Optional.ofNullable(distanceLimitSwitch).ifPresent(config -> locationVO.setDistanceLimitSwitch(NumberUtil.parseInt(config.getParamValue(), -1)));
        return locationVO;
    }

    @Override
    public DrugStoreInquiryPermissionVO getDrugStoreInquiryPermission() {
        // 1.先判断设置(1.全局 2.门店)
        TenantDto tenant = tenantApi.getTenant();
        InquiryOptionConfigRespDto inquiryOptionConfig = inquiryOptionConfigService.getInquiryOptionConfig(tenant, InquiryOptionTypeEnum.PROC_INQUIRY_SERVICE_SWITCH);
        log.info("门店id：{}，门店问诊权限查询结果：{}", JSON.toJSONString(tenant), JSON.toJSONString(inquiryOptionConfig));
        Integer inquiryServer = tenantParamConfigService.getParamConfigValueSys2SelfInteger(TenantParamConfigTypeEnum.INQUIRY_SERVER);
        log.info("门店问诊开关：{}", JSON.toJSONString(inquiryServer));
        if (Boolean.FALSE.equals(inquiryOptionConfig.getProcInquiryServiceSwitch()) || CommonStatusEnum.isDisable(inquiryServer)) {
            return DrugStoreInquiryPermissionVO.builder().build();
        }

        // 2.获取门店可用问诊套餐 的问诊方式
        List<TenantPackageCostDO> costs = tenantPackageCostService.availableInquiryCosts(BizTypeEnum.HYWZ, null);
        log.info("门店可用问诊套餐：{}", JSON.toJSONString(costs));

        // 3.查询当前登录用户信息
        AdminUserRespDTO user = userApi.getUser();
        List<AppHomeCoreMeunEnum> matchedMenus = new ArrayList<>();
        if (user != null && !org.springframework.util.CollectionUtils.isEmpty(user.getRoleCodes())) {
            matchedMenus = AppHomeCoreMeunEnum.getMatchedMenus(MenuConvert.INSTANCE.convert(costs, user));
        }

        // 如果存在药店问诊 且有处方类型的套餐，仅处理处方类型的套餐
        List<InquiryWayTypeEnum> prescriptionTypeInquiryWayTypes = costs.stream().filter(c ->
                Objects.equals(c.getInquiryBizType(), InquiryBizTypeEnum.DRUGSTORE_INQUIRY.getCode()) && CollUtil.isNotEmpty(c.getExtOrDefault().getPrescriptionValue()))
            .map(TenantPackageCostDO::getInquiryWayType).map(InquiryWayTypeEnum::fromCode).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(prescriptionTypeInquiryWayTypes)) {
            return MenuConvert.INSTANCE.convert(prescriptionTypeInquiryWayTypes, matchedMenus);
        }

        // 此处问诊方式只处理药店问诊
        List<InquiryWayTypeEnum> inquiryWayTypes = costs.stream().filter(c ->
                Objects.equals(c.getInquiryBizType(), InquiryBizTypeEnum.DRUGSTORE_INQUIRY.getCode()))
            .map(TenantPackageCostDO::getInquiryWayType).map(InquiryWayTypeEnum::fromCode).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        log.info("门店问诊方式：{},{}", JSON.toJSONString(inquiryWayTypes), matchedMenus);

        DrugStoreInquiryPermissionVO convert = MenuConvert.INSTANCE.convert(inquiryWayTypes, matchedMenus);
        log.info("门店问诊权限：{}", JSON.toJSONString(convert));
        return convert;
    }


    @Override
    public List<DrugStorePackageRespVO> getDrugStorePackageList(DrugStorePackageReqVO reqVO) {
        // 查门店套餐包
        List<TenantPackageRelationRespDto> packageRelationRespDtos = tenantPackageApi.selectTenantPackages(DrugStoreBaseInfoConvert.INSTANCE.convertPackage(reqVO));
        // 查询共享套餐包
        packageRelationRespDtos.addAll(tenantPackageApi.selectTenantSharePackages(DrugStoreBaseInfoConvert.INSTANCE.convertPackage(reqVO)));

        if (CollUtil.isEmpty(packageRelationRespDtos)) {
            return null;
        }
        // 构建套餐包剩余额度信息
        List<TenantPackageRelationRespDto> respVOList = buildDrugStorePackageList(reqVO.getBizType(), packageRelationRespDtos, true);
        List<DrugStorePackageRespVO> list = DrugStoreBaseInfoConvert.INSTANCE.convertPackageVos(respVOList);
        // app端生效中
        if (Objects.equals(reqVO.getEffective(), TenantPackageEffectiveStatusEnum.EFFECT.getCode())) {
            return list.stream().filter(l -> l.getEffective().equals(TenantPackageEffectiveStatusEnum.EFFECT.getCode())).collect(Collectors.toList());
        }
        // app端待生效
        if (Objects.equals(reqVO.getEffective(), TenantPackageEffectiveStatusEnum.UN_EFFECT.getCode())) {
            return list.stream().filter(l -> l.getEffective().equals(TenantPackageEffectiveStatusEnum.UN_EFFECT.getCode())).collect(Collectors.toList());
        }
        // pc端所有
        return list;
    }


    @Override
    public List<TenantPackageRelationRespDto> getDrugStorePackageListByTenantId(DrugStorePackageReqVO reqVO) {
        // 查门店套餐包
        List<TenantPackageRelationRespDto> packageRelationRespDtos = tenantPackageApi.selectTenantPackages(DrugStoreBaseInfoConvert.INSTANCE.convertPackage(reqVO));
        // 查询共享套餐包
        packageRelationRespDtos.addAll(tenantPackageApi.selectTenantSharePackages(DrugStoreBaseInfoConvert.INSTANCE.convertPackage(reqVO)));

        if (CollUtil.isEmpty(packageRelationRespDtos)) {
            return List.of();
        }
        return buildDrugStorePackageList(reqVO.getBizType(), packageRelationRespDtos, true);
    }

    @Override
    public DrugStorePackageRespVO getDrugStoreCurrentEffectivePackage() {
        // 取生效中,到期时间最短的
        List<TenantPackageCostDO> packageCostDOS = tenantPackageCostService.queryTenantPackageCostByCondition(TenantPackageCostReqVO.builder()
            .tenantId(TenantContextHolder.getRequiredTenantId()).bizType(BizTypeEnum.HYWZ.getCode()).inServerTime(true).build());
        TenantPackageCostDO packageCostDO = packageCostDOS.stream().filter(p -> TenantPackageConstant.isUnlimitedCost(p.getSurplusCost()) || p.getSurplusCost() > 0)
            .min(Comparator.comparing(TenantPackageCostDO::getEndTime)).orElse(null);
        if (packageCostDO == null) {
            return null;
        }
        // 组装套餐包信息
        List<TenantPackageRelationRespDto> packageRelationRespDtos = tenantPackageApi.selectTenantPackages(TenantPackageReqDto.builder()
            .tenantId(TenantContextHolder.getRequiredTenantId())
            .tenantPackageIds(Collections.singletonList(packageCostDO.getTenantPackageId())).build());
        if (CollUtil.isEmpty(packageRelationRespDtos)) {
            return null;
        }
        return DrugStoreBaseInfoConvert.INSTANCE.convertPackageVo(packageRelationRespDtos.getFirst());

    }

    @Override
    public PageResult<TenantPackageRelationRespDto> pageDrugStorePackageList(TenantPackageRelationPageReqDto reqDto) {

        PageResult<TenantPackageRelationRespDto> pageResult = tenantPackageApi.pageTenantPackageRelation(reqDto);
        // 构建套餐包剩余额度信息
        List<TenantPackageRelationRespDto> respVOList = buildDrugStorePackageList(null, pageResult.getList(), false);
        return new PageResult<>(respVOList, pageResult.getTotal());
    }

    private List<TenantPackageRelationRespDto> buildDrugStorePackageList(Integer bizType, List<TenantPackageRelationRespDto> packageRelationRespDtos, boolean sort) {
        List<Long> tenantPackageIds = packageRelationRespDtos.stream().map(TenantPackageRelationRespDto::getId).collect(Collectors.toList());
        Map<String, List<TenantPackageCostDO>> costMap = TenantUtils.executeIgnore(() -> tenantPackageCostService.queryTenantPackageCostByCondition(TenantPackageCostReqVO.builder()
                .tenantPackageIds(tenantPackageIds).tenantIds(CollectionUtils.convertSet(packageRelationRespDtos, TenantPackageRelationRespDto::getTenantId)).bizType(bizType).build()))
            .stream().collect(Collectors.groupingBy(a -> a.getTenantId() + "#" + a.getTenantPackageId()));

        List<TenantPackageRelationRespDto> respVOList = packageRelationRespDtos.stream().peek(p -> {
            long until = LocalDateTime.now().until(p.getEndTime(), ChronoUnit.DAYS);
            if (until < 30) { // 如果小于30天到期
                p.setEndDay(until);
            }
            List<TenantPackageCostDO> packageCostDOS = costMap.get(p.getTenantId() + "#" + p.getId());
            if (CollUtil.isNotEmpty(packageCostDOS)) {
                p.setSurplusCosts(TenantPackageCostConvert.INSTANCE.convertSurplusInquiryPackageItems(packageCostDOS));
            }
        }).toList();

        if (sort) {
            //  生效中 + 结束时间 + 剩余额度最小 排序
            return respVOList.stream().sorted(Comparator.comparing(TenantPackageRelationRespDto::getEffective).thenComparing(TenantPackageRelationRespDto::getEndTime)
                .thenComparing(a -> Optional.ofNullable(a.getSurplusCosts()).orElse(List.of()).stream().map(InquiryPackageItem::getCount).min(Long::compare).orElse(0L))).toList();

        }
        return respVOList;

    }


    @Override
    public CommonResult<InquiryOptionConfigRespDto> getInquiryRuleConfig() {
        // 问诊设置
        InquiryOptionConfigRespDto optionConfig = inquiryOptionConfigService.getInquiryOptionConfig(tenantApi.getTenant(), InquiryOptionTypeEnum.getInquiryFrontFormProcessOptions());

        // 特殊处理套餐
        specialHandleConfig(optionConfig);

        // 门店设置
        Map<Integer, TenantParamConfigDO> paramConfigMap = tenantParamConfigService.getTenantParamConfigMap(TenantContextHolder.getRequiredTenantId(),
            List.of(TenantParamConfigTypeEnum.INQUIRY_CHINESE_MEDICINE_BRING, TenantParamConfigTypeEnum.INQUIRY_WESTERN_MEDICINE_BRING));

        TenantParamConfigConvert.INSTANCE.fillInquiryOptionConfig(optionConfig, paramConfigMap);

        return CommonResult.success(optionConfig);
    }

    private void specialHandleConfig(InquiryOptionConfigRespDto optionConfig) {
        // 找到药店问诊 且有额度的
        List<TenantPackageCostDO> costs = tenantPackageCostService.availableInquiryCosts(BizTypeEnum.HYWZ, InquiryBizTypeEnum.DRUGSTORE_INQUIRY);

        if (CollUtil.isEmpty(costs)) {
            return;
        }

        // 1.处理问诊套餐医院身份证必填
        List<String> hosPrefs = costs.stream().flatMap(c -> Optional.ofNullable(c.getHospitalPrefs()).orElse(List.of()).stream()).distinct().toList();
        if (CollUtil.isNotEmpty(hosPrefs)) {
            if (ObjectUtil.equal(optionConfig.getProcIdentityRequired(), Boolean.TRUE)) {
                // 如果医院是空 则说明配置查询到得是门店，则为true 。如果医院不是空 则说明配置查询到得是区域，校验可选医院在不在配置里,不存在就是false
                if (CollUtil.isNotEmpty(optionConfig.getAreaInquiryHospitalPrefs())
                    && !CollUtil.containsAny(optionConfig.getAreaInquiryHospitalPrefs(), hosPrefs)) {
                    optionConfig.setProcIdentityRequired(false);
                }
            }
        }

        // 2. 填充处方类型字典
        List<Integer> prescriptionValue = costs.stream().flatMap(c -> Optional.ofNullable(c.getExtOrDefault().getPrescriptionValue()).orElse(List.of()).stream()).distinct().toList();
        if (CollUtil.isNotEmpty(prescriptionValue)) {
            optionConfig.setPrescriptionTypeDict(dictDataApi.getDictDataList(DictTypeConstants.PRESCRIPTION_TYPE).stream().filter(d -> CollUtil.contains(prescriptionValue, NumberUtil.parseInt(d.getValue(), -1))).toList());
        }
    }

    @Override
    public CommonResult<InquiryOptionConfigRespDto> getInquiryRuleConfigDoctor(Long tenantId, Integer prescriptionType) {

        Long tid = Optional.ofNullable(tenantId).orElse(TenantContextHolder.getRequiredTenantId());
        InquiryOptionConfigRespDto optionConfigRespDto = TenantUtils.execute(tid,
            () -> inquiryOptionConfigService.getInquiryOptionConfig(tenantApi.getTenant(), InquiryOptionTypeEnum.getInquiryDoctorProcessOptions()));

        // 切换目录调整问诊设置
        if (transmissionConfigApi.isProductChangeQueryCatalog(tid, prescriptionType)) {
            optionConfigRespDto.setFormTcmSyndromeDoc(optionConfigRespDto.getFormTcmSyndrome());
            optionConfigRespDto.setFormTcmSyndromeDocRequired(optionConfigRespDto.getFormTcmSyndromeRequired());
            optionConfigRespDto.setSwitchProductCategory(true);
        }

        return CommonResult.success(optionConfigRespDto);
    }

    @Override
    public List<TenantPackageCostDto> selectUserTenantPackages(Long userId, List<TenantPackageEffectiveStatusEnum> effectiveStatusEnums) {
        List<TenantUserRelationDto> tenantListByUserId = tenantUserRelationApi.getAvailableTenantListByUserId(userId);
        if (CollUtil.isEmpty(tenantListByUserId)) {
            return null;
        }
        List<TenantPackageCostDO> costDOList = TenantUtils.executeIgnore(() -> tenantPackageCostService.queryTenantPackageCostByCondition(TenantPackageCostReqVO.builder()
            .tenantIds(CollectionUtils.convertSet(tenantListByUserId, TenantUserRelationDto::getTenantId))
            .geEndTime(LocalDateTime.now()).status(TenantPackageRelationStatusEnum.NORMAL.getCode()).build()));
        if (CollUtil.isEmpty(costDOList)) {
            return null;
        }
        // 找出符合生效条件得套餐
        return costDOList.stream().filter(c -> {
            InquiryPackageItem packageItem = InquiryPackageItem.builder()
                .inquiryWayType(c.getInquiryWayType())
                .count(c.getSurplusCost())
                .unlimited(TenantPackageConstant.isUnlimitedCost(c.getSurplusCost())).build();
            Integer effectiveStatus = TenantPackageEffectiveStatusEnum.getEffectiveStatus(c.getStartTime(), c.getEndTime(), TenantPackageRelationStatusEnum.fromStatusCode(c.getStatus()), Collections.singletonList(packageItem));
            return effectiveStatusEnums.stream().anyMatch(e -> Objects.equals(e.getCode(), effectiveStatus));
        }).map(TenantPackageCostConvert.INSTANCE::convertDto).toList();
    }

    /**
     * 判断传入的租户id是否可问诊
     *
     * @param tenantId
     * @return
     */
    @Override
    public DrugStoreInquiryPermissionVO tenantCanInquiry(Long tenantId) {
        // 1、查询当前门店是否禁用
        TenantDto tenantDto = tenantApi.getTenant(tenantId);
        if (ObjectUtil.equals(tenantDto.getStatus(), CommonStatusEnum.DISABLE.getStatus())) {
            throw exception(TENANT_DISABLE, tenantDto.getName());
        }
        // 2、查询当前门店是否有可问诊的套餐额度
        List<TenantPackageCostDO> costs = TenantUtils.execute(tenantId, () -> tenantPackageCostService.availableInquiryCosts(BizTypeEnum.HYWZ, InquiryBizTypeEnum.DRUGSTORE_INQUIRY));

        List<Integer> inquiryWayTypes = costs.stream().map(TenantPackageCostDO::getInquiryWayType).filter(Objects::nonNull).distinct().toList();

        if (org.springframework.util.CollectionUtils.isEmpty(inquiryWayTypes)) {
            throw exception(TENANT_PACKAGE_INQUIRY_NO_ERROR);
        }
        // 3、返回门店信息以及问诊权限信息
        return DrugStoreBaseInfoConvert.INSTANCE.convertPermissionVO(inquiryWayTypes, tenantDto);
    }
}