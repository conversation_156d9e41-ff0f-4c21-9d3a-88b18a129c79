package com.xyy.saas.inquiry.drugstore.server.controller.app.drugstore;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static com.xyy.saas.inquiry.enums.tenant.TenantPackageEffectiveStatusEnum.EFFECT;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.api.dict.DictDataApi;
import cn.iocoder.yudao.module.system.api.dict.dto.DictDataRespDTO;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageRelationPageReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageRelationRespDto;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigRespDto;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.DrugStorePackageReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.DrugStorePackageRespVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.DrugstoreQrCodeRespVO;
import com.xyy.saas.inquiry.drugstore.server.controller.app.drugstore.vo.DrugStoreInquiryPermissionVO;
import com.xyy.saas.inquiry.drugstore.server.controller.app.drugstore.vo.LocationVO;
import com.xyy.saas.inquiry.drugstore.server.service.tenant.DrugStoreBaseInfoService;
import com.xyy.saas.inquiry.drugstore.server.service.tenant.DrugstoreQrCodeService;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "APP+PC-门店基础信息")
@RestController
@RequestMapping(value = {"/admin-api/kernel/drugstore/base-info", "/app-api/kernel/drugstore/base-info"}, produces = "application/json")
@Validated
public class AppDrugStoreBaseInfoController {

    @Resource
    private DrugStoreBaseInfoService drugStoreBaseInfoService;

    @Resource
    private DrugstoreQrCodeService drugstoreQrCodeService;

    @Resource
    private DictDataApi dictDataApi;


    @GetMapping(value = "/package-page")
    @Operation(summary = "分页获得门店套餐信息-分页-运营后台")
    @PreAuthorize("@ss.hasPermission('system:tenant-package-relation:query')")
    public CommonResult<PageResult<TenantPackageRelationRespDto>> pageDrugStorePackageList(@Valid TenantPackageRelationPageReqDto reqDto) {
        return success(drugStoreBaseInfoService.pageDrugStorePackageList(reqDto));
    }


    @GetMapping("/package-list")
    @RequestMapping("/package-list")
    @Operation(summary = "获得门店套餐信息-app+pc")
    public CommonResult<List<DrugStorePackageRespVO>> getDrugStorePackageList(@Valid DrugStorePackageReqVO reqVO) {
        return success(drugStoreBaseInfoService.getDrugStorePackageList(reqVO));
    }

    @GetMapping("/package-list-by-tenantId")
    @PermitAll
    @Operation(summary = "根据租户id获取门店套餐，内部客服系统使用")
    public CommonResult<List<Map<String,Object>>> getDrugStorePackageListByTenantId(@RequestParam("tenantId") Long tenantId) {
        DrugStorePackageReqVO reqVO = new DrugStorePackageReqVO();
        reqVO.setTenantId(tenantId);
        reqVO.setBizType(BizTypeEnum.HYWZ.getCode());
        TenantContextHolder.setTenantId(tenantId);
        List<TenantPackageRelationRespDto> drugStorePackageList = drugStoreBaseInfoService.getDrugStorePackageListByTenantId(reqVO);
        if(CollUtil.isEmpty(drugStorePackageList)){
            return success(List.of());
        }
        Map<String, String> dictMap = Optional.ofNullable(dictDataApi.getDictDataList(1L, DictTypeConstants.TENANT_PACKAGE_RELATION_SIGN_CHANNEL)).orElse(List.of())
            .stream().collect(Collectors.toMap(DictDataRespDTO::getValue, DictDataRespDTO::getLabel, (a, b) -> b));

        //保留生效中
        return success(JsonUtils.parseObject(JsonUtils.toJsonString(drugStorePackageList.stream().filter(e -> Objects.equals(e.getEffective(), EFFECT.getCode())).toList()), new TypeReference<List<Map<String, Object>>>() {})
            .stream().peek(e -> e.put("signChannelVal", dictMap.get(String.valueOf((Integer)e.get("signChannel"))))).toList());
    }

    @GetMapping("/current-effective-package")
    @Operation(summary = "获得门店当前生效套餐-一条")
    public CommonResult<DrugStorePackageRespVO> getDrugStoreCurrentEffectivePackage() {
        return success(drugStoreBaseInfoService.getDrugStoreCurrentEffectivePackage());
    }


    @PostMapping("/save-location")
    @Operation(summary = "app保存门店定位", description = "前端获取用户定位后传入")
    public CommonResult<Boolean> saveLocation(@Valid @RequestBody LocationVO locationVO) {
        drugStoreBaseInfoService.saveLocation(locationVO);
        return success(true);
    }

    @GetMapping("/get-location")
    @Operation(summary = "app获取门店定位+离店限制", description = "前端获取用户定位后传入")
    public CommonResult<LocationVO> getLocation() {
        return success(drugStoreBaseInfoService.getLocation());
    }


    @GetMapping("/inquiry-permission")
    @Operation(summary = "app首页菜单获取", description = "用户登录后,查询首页有哪些菜单,用于控制前端展示")
    public CommonResult<DrugStoreInquiryPermissionVO> getDrugStoreInquiryPermission() {
        DrugStoreInquiryPermissionVO inquiryPermissionVO = drugStoreBaseInfoService.getDrugStoreInquiryPermission();
        return success(inquiryPermissionVO);
    }

    @GetMapping("/can-inquiry")
    @Operation(summary = "当前门店是否可发起问诊", description = "判断当前门店是否可问诊")
    @Parameter(name = "tenantId", description = "租户id", required = true)
    @PermitAll
    public CommonResult<DrugStoreInquiryPermissionVO> tenantCanInquiry(@RequestParam("tenantId") Long tenantId) {
        return success(drugStoreBaseInfoService.tenantCanInquiry(tenantId));
    }

    @GetMapping("/get-inquiry-mp-qrcode")
    @Operation(summary = "获取or创建门店问诊公众号二维码")
    @PreAuthorize("@ss.hasPermission('drugstore:qrcode:query')")
    public CommonResult<DrugstoreQrCodeRespVO> getOrCreateInquiryMpQrCode() {
        DrugstoreQrCodeRespVO qrCode = drugstoreQrCodeService.getDrugstoreWxMpQrCode();
        return success(qrCode);
    }


    @GetMapping("/get-inquiry-ma-qrcode")
    @Operation(summary = "获取or创建门店问诊小程序二维码")
    @PreAuthorize("@ss.hasPermission('drugstore:qrcode:query')")
    public CommonResult<DrugstoreQrCodeRespVO> getOrCreateInquiryMaQrCode() {
        DrugstoreQrCodeRespVO qrCode = drugstoreQrCodeService.getDrugstoreWxMaQrCode();
        return success(qrCode);
    }


    @GetMapping("/get-inquiry-rule-config")
    @Operation(summary = "获取当前门店问诊规则配置", description = "(null或false =false)-eg:身份证必填/监护人必填/带出药品等")
    public CommonResult<InquiryOptionConfigRespDto> getInquiryRuleConfig() {
        return drugStoreBaseInfoService.getInquiryRuleConfig();
    }

    @GetMapping("/get-inquiry-rule-config-doctor")
    @Operation(summary = "医生获取当前门店问诊规则配置", description = "(null或false =false)-eg:身份证必填/监护人必填/带出药品等")
    public CommonResult<InquiryOptionConfigRespDto> getInquiryRuleConfigDoctor(
        @RequestParam(value = "tenantId", required = false) Long tenantId,
        @RequestParam(value = "prescriptionType", required = false) Integer prescriptionType) {
        return drugStoreBaseInfoService.getInquiryRuleConfigDoctor(tenantId, prescriptionType);
    }

}