<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.drugstore.server.dal.mysql.option.InquiryOptionConfigMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->

  <insert id="insertOrUpdateBatch" parameterType="com.xyy.saas.inquiry.drugstore.server.dal.dataobject.option.InquiryOptionConfigDO">
    INSERT INTO saas_inquiry_option_config (target_type, option_type, target_id, target_name, province, province_code, city, city_code, area, area_code, option_name, option_value, description, used,ext, deleted,creator)
    VALUES
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.targetType}, #{item.optionType}, #{item.targetId}, #{item.targetName}, #{item.province}, #{item.provinceCode}, #{item.city}, #{item.cityCode}, #{item.area}, #{item.areaCode}, #{item.optionName}, #{item.optionValue},
      #{item.description}, #{item.used}, #{item.ext}, #{item.deleted}, #{item.creator})
    </foreach>
    ON DUPLICATE KEY UPDATE
    target_name = values(target_name),
    province = values(province),
    province_code = values(province_code),
    city = values(city),
    city_code = values(city_code),
    area = values(area),
    area_code = values(area_code),
    option_name = values(option_name),
    option_value = values(option_value),
    description = values(description),
    used = values(used),
    ext = values(ext),
    deleted = values(deleted),
    creator = values(creator)
  </insert>

  <select id="queryAllByTenantAndOptionType" resultType="com.xyy.saas.inquiry.drugstore.server.dal.dataobject.option.InquiryOptionConfigDO">
    SELECT * FROM saas_inquiry_option_config
    WHERE deleted = 0
    AND target_type = ${@<EMAIL>()}
    AND target_id = ${@com.xyy.saas.inquiry.constant.TenantConstant@DEFAULT_TENANT_ID}
    <if test="optionTypeList != null and optionTypeList.size() > 0">
      and option_type in
      <foreach collection="optionTypeList" open="(" item="id" separator="," close=")">
        #{id}
      </foreach>
    </if>

    UNION ALL

    SELECT * FROM saas_inquiry_option_config
    WHERE deleted = 0
    AND target_type = ${@<EMAIL>()}
    AND target_id = CAST(#{tenant.areaCode} AS SIGNED)
    <if test="optionTypeList != null and optionTypeList.size() > 0">
      and option_type in
      <foreach collection="optionTypeList" open="(" item="id" separator="," close=")">
        #{id}
      </foreach>
    </if>

    UNION ALL

    SELECT * FROM saas_inquiry_option_config
    WHERE deleted = 0
    AND target_type = ${@<EMAIL>()}
    AND target_id = #{tenant.id}
    <if test="optionTypeList != null and optionTypeList.size() > 0">
      and option_type in
      <foreach collection="optionTypeList" open="(" item="id" separator="," close=")">
        #{id}
      </foreach>
    </if>
  </select>
</mapper>