package com.xyy.saas.inquiry.drugstore.api.option.dto;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.system.api.dict.dto.DictDataRespDTO;
import com.xyy.saas.inquiry.pojo.condition.ConditionGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 门店变更额度Dto
 *
 * @Author:chenxiaoyi
 * @Date:2024/09/05 13:56
 */
@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquiryOptionConfigRespDto implements Serializable {

    // region 问诊单配置

    // region 问诊单配置 - 超适应症审查
    /**
     * 问诊单配置-超适应症审查
     */
    @Schema(description = "超适应症审查", example = "false")
    private Boolean formOffLabelReview;

    // endregion

    // region 问诊单配置 - 用法用量审查
    /**
     * 问诊单配置-用法用量审查
     */
    @Schema(description = "用法用量审查", example = "false")
    private Boolean formUsageDosageReview;

    // endregion

    // region 问诊单配置 - 抗菌药品配置
    /**
     * 问诊单配置-抗菌药品配置
     */
    @Schema(description = "抗菌药品配置", example = "false")
    private Boolean formAntimicrobialDrug;

    /**
     * 抗菌药品配置-分级目录id
     */
    // @Schema(description = "分级目录id", example = "1")
    private Long formAntimicrobialDrugCatalogId;
    /**
     * 抗菌药品配置-分级目录名称
     */
    // @Schema(description = "分级目录名称", example = "分级目录名称")
    private String formAntimicrobialDrugCatalogName;
    /**
     * 抗菌药品配置-分级目录文件链接
     */
    // @Schema(description = "分级目录文件链接", example = "http://www.baidu.com")
    private String formAntimicrobialDrugCatalogUrl;

    // endregion

    @Schema(description = "西医诊断填写", example = "true")
    private Boolean formWesternMedicineDiagnosis;

    @Schema(description = "西医诊断填写 - 是否必填", example = "true")
    private Boolean formWesternMedicineDiagnosisRequired;

    @Schema(description = "中医辨证、治法填写", example = "true")
    private Boolean formTcmSyndromeTreatmentMethods;

    @Schema(description = "中医辨证、治法填写 - 是否必填", example = "true")
    private Boolean formTcmSyndromeTreatmentMethodsRequired;


    @Schema(description = "中医辨证填写", example = "true")
    private Boolean formTcmSyndrome;

    @Schema(description = "中医辨证填写 - 是否必填", example = "true")
    private Boolean formTcmSyndromeRequired;


    @Schema(description = "中医辨证填写-医生端", example = "true")
    private Boolean formTcmSyndromeDoc;

    @Schema(description = "中医辨证填写-医生端 - 是否必填", example = "true")
    private Boolean formTcmSyndromeDocRequired;


    @Schema(description = "中医治法填写", example = "true")
    private Boolean formTcmTreatmentMethods;

    @Schema(description = "中医治法填写 - 是否必填", example = "true")
    private Boolean formTcmTreatmentMethodsRequired;


    @Schema(description = "问诊单配置", example = "true")
    private Boolean formInterviewStyle;
    // endregion

    // region 问诊流程配置

    // region 问诊流程配置 - 身份证必填
    /**
     * 问诊流程配置-身份证必填
     */
    @Schema(description = "身份证必填", example = "false")
    private Boolean procIdentityRequired;

    // endregion

    // region 问诊流程配置 - 处方类型填写开关
    /**
     * 问诊流程配置-处方类型填写开关
     */
    @Schema(description = "处方类型填写开关", example = "false")
    private Boolean procPrescriptionTypeInput;
    /**
     * 问诊流程配置-处方类型填写开关-是否必填
     */
    @Schema(description = "处方类型填写-是否必填", example = "false")
    private Boolean procPrescriptionTypeRequired;

    @Schema(description = "小程序问诊是否需要门店审核", example = "false")
    private Boolean procWeChatInquiryStoreAudit;


    @Schema(description = "小程序问诊患者是否可查看处方", example = "false")
    private Boolean procWeChatInquiryPatientViewPrescription;

    // endregion

    // region 问诊流程配置 - 家庭地址填写开关
    /**
     * 问诊流程配置-家庭地址填写开关
     */
    @Schema(description = "家庭地址填写开关", example = "false")
    private Boolean procHomeAddressInput;
    /**
     * 问诊流程配置-家庭住址填写开关-是否必填
     */
    @Schema(description = "家庭住址填写-是否必填", example = "false")
    private Boolean procHomeAddressRequired;

    // endregion

    // region 问诊流程配置 - 监护人填写开关
    /**
     * 问诊流程配置-监护人填写开关
     */
    @Schema(description = "监护人填写开关", example = "false")
    private Boolean procGuardianInput;

    @Schema(description = "监护人填写年龄最低限度,如果<=6岁 则页面需要填写监护人信息", example = "6")
    private Integer procGuardianAge;
    /**
     * 问诊流程配置-监护人填写开关-是否必填
     */
    @Schema(description = "监护人填写-是否必填", example = "false")
    private Boolean procGuardianRequired;

    // endregion

    // region 问诊流程配置 - 问诊合规配置
    /**
     * 问诊流程配置-问诊合规配置(年龄/妊娠哺乳期)
     */
    @Schema(description = "问诊合规配置", example = "false")
    private Boolean procInquiryCompliance;

    /**
     * 问诊合规配置-患者年龄限制-大于等于
     */
    @Schema(description = "患者年龄限制-大于等于", example = ">=10")
    private Integer procInquiryComplianceForPatientAgeGe;

    /**
     * 问诊合规配置-患者年龄限制-且小于
     */
    @Schema(description = "患者年龄限制-小于", example = "<100")
    private Integer procInquiryComplianceForPatientAgeLt;

    /**
     * 问诊合规配置-妊娠哺乳是否可发起问诊
     */
    @Schema(description = "妊娠哺乳是否可发起问诊", example = "false")
    private Boolean procInquiryComplianceAllowForPregnancyLactation;

    // endregion

    // region 问诊流程配置 - 录屏问诊配置
    /**
     * 问诊流程配置-录屏问诊配置
     */
    private Boolean procVideoInquiry;

    /**
     * 录屏问诊配置-比例
     */
    private Integer procVideoInquiryRatio;

    // endregion

    // region 问诊流程配置 - 取消预购药品
    /**
     * 问诊流程配置-取消预购药品配置
     */
    @Schema(description = "取消预购药品配置", example = "false")
    private Boolean procCancelPreorderDrug;

    @Schema(description = "取消临时新增药品", example = "false")
    private Boolean procCancelAddNewDrug;

    // endregion

    // region 问诊流程配置 - 图文交互对话弹出速度
    /**
     * 问诊流程配置-图文交互对话弹出速度
     */
    @Schema(description = "图文交互对话弹出速度", example = "500ms")
    private Integer procTeletextInteractionDialogPopupSpeed;

    // region 问诊流程配置 - 图文交互对话弹出速度
    /**
     * 远程审方问诊表单-远程审方开方时间最大距今多少天
     */
    @Schema(description = "远程审方开方时间最大距今多少天", example = "15")
    private Integer procRemotePrescriptionOpenDateMax;

    // endregion

    // region 问诊流程配置 - 问诊服务开关
    /**
     * 问诊流程配置-问诊服务开关
     */
    // 全局配置：默认开启，切换禁用
    // 地区&门店配置：配置地区&门店 禁用
    private Boolean procInquiryServiceSwitch;

    // endregion

    // region 问诊流程配置 - 医生接诊默认页面
    /**
     * 问诊流程配置-医生接诊默认页面
     */
    @Schema(description = "医生接诊默认页面", example = "defaultPage")
    private String procDoctorAdmissionDefaultPage;

    // endregion

    @Schema(description = "上传复诊证明", example = "true")
    private Boolean procUploadFollowupCertificate;
    // 上传复诊证明 - 是否必填
    private Boolean procUploadFollowupCertificateRequired;

    @Schema(description = "病历书写", example = "true")
    private Boolean procCaseRecord;

    @Schema(description = "处方划价", example = "true")
    private Boolean procPrescriptionPricing;


    @Schema(description = "视频远程审方", example = "true")
    private Boolean procRemoteAuditPrescriptionVideo;

    // endregion

    // region 医生开方配置

    // region 医生开方配置 - 开方基础属性配置

    // region 医生开方配置 - 开方基础属性配置 - 全局处方日期格式
    /**
     * 医生开方配置-开方基础属性配置-全局处方日期格式
     */
    // @Schema(description = "全局处方日期格式", example = "yyyy-MM-dd")
    private String presGlobalDateFormat;

    // endregion

    // region 医生开方配置 - 开方基础属性配置 - 图文问诊最大可接单数量
    /**
     * 医生开方配置-开方基础属性配置-图文问诊医生可同时最大接单数量
     */
    private Integer presTeletextInquiryDoctorOrderMax;

    // endregion

    // region 医生开方配置 - 开方基础属性配置 - 开方二次确认弹窗
    /**
     * 医生开方配置-开方基础属性配置-医生开方二次确认弹窗
     */
    @Schema(description = "医生开方二次确认弹窗", example = "false")
    private Boolean presDoctor2ndConfirmDialog;

    // endregion

    // region 医生开方配置 - 开方基础属性配置 - 处方签名使用时间间隔
    /**
     * 医生开方配置-开方基础属性配置-处方签名使用时间间隔
     */
    private Integer presSignatureUseInterval;

    // endregion

    // region 医生开方配置 - 开方基础属性配置 - 医生不可接诊区域
    /**
     * 医生开方配置-开方基础属性配置-医生不可接诊区域
     */
    private String presDoctorCannotInquiryArea;

    // endregion

    // region 医生开方配置 - 开方基础属性配置 - 自动开方配置

    // region 医生开方配置 - 开方基础属性配置 - 自动开方配置 - 过敏史是否流向真人问诊
    /**
     * 医生开方配置-开方基础属性配置-自动开方配置-过敏史是否流向真人问诊
     */
    private Boolean presAutoInquiryToRealForAllergic;

    // endregion

    // region 医生开方配置 - 开方基础属性配置 - 自动开方配置 - 肝、肾功能异常是否流向真人问诊
    /**
     * 医生开方配置-开方基础属性配置-自动开方配置-肝、肾功能异常是否流向真人问诊
     */
    private Boolean presAutoInquiryToRealForLiverRenalDysfunction;

    // endregion

    // region 医生开方配置 - 开方基础属性配置 - 自动开方配置 - 妊娠、哺乳期是否流向真人问诊
    /**
     * 医生开方配置-开方基础属性配置-自动开方配置-妊娠、哺乳期是否流向真人问诊
     */
    private Boolean presAutoInquiryToRealForPregnancyLactation;

    // endregion

    // region 医生开方配置 - 开方基础属性配置 - 自动开方配置 - 多诊断是否流向真人问诊
    /**
     * 医生开方配置-开方基础属性配置-自动开方配置-多诊断是否流向真人问诊
     */
    private Boolean presAutoInquiryToRealForMultiDiagnose;

    /**
     * 多诊断是否流向真人问诊-流入比例
     */
    private Integer presAutoInquiryToRealForMultiDiagnoseRatio;

    /**
     * 多诊断是否流向真人问诊-条件组规则
     */
    private List<ConditionGroup> presAutoInquiryToRealForMultiDiagnoseConditions;

    // endregion

    // region 医生开方配置 - 开方基础属性配置 - 自动开方配置 - 特定年龄区间是否流向真人问诊
    /**
     * 医生开方配置-开方基础属性配置-自动开方配置-特定年龄区间是否流向真人问诊
     */
    private Boolean presAutoInquiryToRealForSpecialAgeRange;

    /**
     * 特定年龄区间是否流向真人问诊-流入比例
     */
    private Integer presAutoInquiryToRealForSpecialAgeRangeRatio;

    /**
     * 特定年龄区间是否流向真人问诊-条件组规则
     */
    private List<ConditionGroup> presAutoInquiryToRealForSpecialAgeRangeConditions;

    // endregion

    // region 医生开方配置 - 开方基础属性配置 - 自动开方配置 - 自动开方是否回流真人
    /**
     * 医生开方配置-开方基础属性配置-自动开方配置-自动开方是否回流真人
     */
    private Boolean presAutoInquiryReturnToReal;

    /**
     * 自动开方是否回流真人-积压数量
     */
    private Integer presAutoInquiryReturnToRealBacklogNum;

    /**
     * 自动开方是否回流真人-回流比例
     */
    private Integer presAutoInquiryReturnToRealRatio;

    // endregion

    // endregion

    // endregion

    // region 医生开方配置 - 医保代码展示
    /**
     * 医生开方配置-医保代码展示
     */
    private Boolean presMedicareCodeDisplay;

    // endregion

    // region 医生开方配置 - 废弃处方
    /**
     * 医生开方配置-废弃处方
     */
    @Schema(description = "废弃处方", example = "false")
    private Boolean presAbandonPrescription;

    // endregion

    // region 医生开方配置 - 全真人问诊
    /**
     * 医生开方配置-全真人问诊
     */
    private Boolean presAllRealPeopleInquiry;


    /**
     * 全真人问诊医院编码
     */
    private List<String> presAllRealPeopleInquiryHospitalPrefs;

    /**
     * area 医院编码
     */
    private List<String> areaInquiryHospitalPrefs;

    // endregion

    // endregion

    // region 药师审方配置
    /**
     * 药师审方配置-医院药师审核
     */
    private Boolean presPharmacistReview;

    // endregion

    /**
     * 商品查询是否切目录
     */
    private boolean isSwitchProductCategory;

    // 门店参数设置

    @Schema(description = "问诊是否带出上次西药", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Boolean inquiryWesternMedicineBring = true;

    @Schema(description = "问诊是否带出上次中药", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Boolean inquiryChineseMedicineBring = true;


    // 处方类型字典值
    private List<DictDataRespDTO> prescriptionTypeDict;


    public InquiryOptionConfigRespDto extToThis(String ext) {
        if (StringUtils.isBlank(ext)) {
            return this;
        }
        InquiryOptionConfigRespDto extObject = JsonUtils.parseObject(ext, InquiryOptionConfigRespDto.class);
        if (extObject == null) {
            return this;
        }
        // null属性忽略
        BeanUtil.copyProperties(extObject, this, CopyOptions.create().ignoreNullValue());
        return this;
    }

    private static volatile Map<String, Field> fieldMap;

    @SuppressWarnings("squid:S3011")
    // 获取所有字段, 加锁
    public static synchronized Field getField(String fieldName) {
        if (fieldMap == null) {
            fieldMap = Arrays.stream(InquiryOptionConfigRespDto.class.getDeclaredFields())
                .filter(f -> !Modifier.isStatic(f.getModifiers()))
                .collect(Collectors.toMap(Field::getName, f -> {
                    f.setAccessible(true);
                    return f;
                }));
        }
        return fieldMap.get(fieldName);
    }


}
