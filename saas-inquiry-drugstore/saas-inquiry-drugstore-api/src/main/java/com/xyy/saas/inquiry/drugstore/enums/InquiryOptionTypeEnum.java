package com.xyy.saas.inquiry.drugstore.enums;

import static com.xyy.saas.inquiry.drugstore.enums.InquiryTargetTypeEnum.AREA;
import static com.xyy.saas.inquiry.drugstore.enums.InquiryTargetTypeEnum.GLOBAL;
import static com.xyy.saas.inquiry.drugstore.enums.InquiryTargetTypeEnum.STORE;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;
import com.xyy.saas.inquiry.enums.pharmacist.PharmacistAuditAreaTypeEnum;
import lombok.Getter;

/**
 * @Desc 问诊配置选项类型枚举
 * <AUTHOR>
 */
@Getter
public enum InquiryOptionTypeEnum implements IntArrayValuable {

    // 问诊单配置
    FORM_OFF_LABEL_REVIEW(10001, "formOffLabelReview", "超适应症审查", "问诊单配置-超适应症审查", STORE, AREA),
    FORM_USAGE_DOSAGE_REVIEW(10002, "formUsageDosageReview", "用法用量审查", "问诊单配置-用法用量审查", STORE, AREA),
    FORM_ANTIMICROBIAL_DRUG(10003, "formAntimicrobialDrug", "抗菌药品配置", "问诊单配置-抗菌药品配置", STORE, AREA),
    FORM_WESTERN_MEDICINE_DIAGNOSIS(10004, "formWesternMedicineDiagnosis", "西医诊断填写", "问诊单配置-西医诊断填写", STORE, AREA),
    FORM_TCM_SYNDROME_TREATMENT_METHODS(10005, "formTcmSyndromeTreatmentMethods", "中医辨证、治法填写", "问诊单配置-中医辨证、治法填写", STORE, AREA),

    FORM_TCM_SYNDROME(10006, "formTcmSyndrome", "中医辨证填写", "问诊单配置-中医辨证填写", STORE, AREA),
    FORM_TCM_TREATMENT_METHODS(10007, "formTcmTreatmentMethods", "中医治法填写", "问诊单配置-中医治法填写", STORE, AREA),
    FORM_FORM_INTERVIEW_STYLE(10008, "formInterviewStyle", "问诊表单样式", "问诊单配置-问诊表单样式", STORE, AREA),


    // 问诊流程配置
    PROC_IDENTITY_REQUIRED(20001, "procIdentityRequired", "身份证必填", "问诊流程配置-身份证必填", STORE, AREA),
    PROC_PRESCRIPTION_TYPE_INPUT(20002, "procPrescriptionTypeInput", "处方类型填写开关", "问诊流程配置-处方类型填写开关", STORE, AREA),
    PROC_HOME_ADDRESS_INPUT(20003, "procHomeAddressInput", "家庭地址填写开关", "问诊流程配置-家庭地址填写开关", STORE, AREA),
    PROC_INQUIRY_COMPLIANCE(20004, "procInquiryCompliance", "问诊合规配置", "问诊流程配置-问诊合规配置(年龄/妊娠哺乳期)", AREA, GLOBAL),
    PROC_VIDEO_INQUIRY(20005, "procVideoInquiry", "录屏问诊配置", "问诊流程配置-录屏问诊配置", STORE, AREA),
    PROC_CANCEL_PREORDER_DRUG(20006, "procCancelPreorderDrug", "取消预购药品配置", "问诊流程配置-取消预购药品配置", STORE, AREA),

    PROC_TELETEXT_INTERACTION_DIALOG_POPUP_SPEED(20007, "procTeletextInteractionDialogPopupSpeed", "图文交互对话弹出速度", "问诊流程配置-图文交互对话弹出速度", GLOBAL),
    // 全局配置：默认开启，切换禁用
    // 地区&门店配置：配置地区&门店 禁用
    PROC_INQUIRY_SERVICE_SWITCH(20008, "procInquiryServiceSwitch", "问诊服务开关", "问诊流程配置-问诊服务开关", GLOBAL, STORE, AREA),
    PROC_DOCTOR_ADMISSION_DEFAULT_PAGE(20009, "procDoctorAdmissionDefaultPage", "医生接诊默认页面", "问诊流程配置-医生接诊默认页面", STORE, AREA, GLOBAL),
    PROC_GUARDIAN_INPUT(20010, "procGuardianInput", "监护人填写开关", "问诊流程配置-监护人填写开关", STORE, AREA),
    PROC_UPLOAD_FOLLOWUP_CERTIFICATE(20011, "procUploadFollowupCertificate", "上传复诊证明", "问诊流程配置-上传复诊证明", STORE, AREA),
    PROC_CASE_RECORD(20012, "procCaseRecord", "病历书写", "问诊流程配置-病历书写", STORE, AREA),
    PROC_PRESCRIPTION_PRICING(20013, "procPrescriptionPricing", "处方划价", "问诊流程配置-处方划价", STORE, AREA),
    PROC_CANCEL_ADD_NEW_DRUG(20014, "procCancelAddNewDrug", "取消临时新增药品", "问诊流程配置-取消临时新增药品", STORE, AREA),
    PROC_REMOTE_PRESCRIPTION_OPEN_DATE_MAX(20015, "procRemotePrescriptionOpenDateMax", "远程审方开方日期最大距今多少天", "问诊流程配置-远程审方开方日期最大距今多少天", GLOBAL),
    PROC_WE_CHAT_INQUIRY_PATIENT_VIEW_PRESCRIPTION(20017, "procWeChatInquiryPatientViewPrescription", "患者查看处方", "患者小程序设置-小程序问诊患者查看处方", STORE, AREA),
    PROC_REMOTE_AUDIT_PRESCRIPTION_VIDEO(20018, "procRemoteAuditPrescriptionVideo", "视频远程审方", "问诊流程配置-视频远程审方", STORE, AREA),
    // 医生开方配置
    /// / 开方基础属性配置
    PRES_GLOBAL_DATE_FORMAT(30001, "presGlobalDateFormat", "全局处方日期格式", "医生开方配置-开方基础属性配置-全局处方日期格式", GLOBAL),
    PRES_TELETEXT_INQUIRY_DOCTOR_ORDER_MAX(30002, "presTeletextInquiryDoctorOrderMax", "图文问诊医生可同时最大接单数量", "医生开方配置-开方基础属性配置-图文问诊医生可同时最大接单数量", GLOBAL),
    PRES_DOCTOR_2ND_CONFIRM_DIALOG(30003, "presDoctor2ndConfirmDialog", "医生开方二次确认弹窗", "医生开方配置-开方基础属性配置-医生开方二次确认弹窗", GLOBAL),
    PRES_SIGNATURE_USE_INTERVAL(30004, "presSignatureUseInterval", "处方签名使用时间间隔", "医生开方配置-开方基础属性配置-处方签名使用时间间隔", GLOBAL),
    PRES_DOCTOR_CANNOT_INQUIRY_AREA(30005, "presDoctorCannotInquiryArea", "医生不可接诊区域", "医生开方配置-开方基础属性配置-医生不可接诊区域", GLOBAL),

    /// / 开方基础属性配置 - 自动开方配置
    PRES_AUTO_INQUIRY_TO_REAL_FOR_ALLERGIC(30061, "presAutoInquiryToRealForAllergic", "过敏史是否流向真人问诊", "医生开方配置-开方基础属性配置-自动开方配置-过敏史是否流向真人问诊", GLOBAL),
    PRES_AUTO_INQUIRY_TO_REAL_FOR_LIVER_RENAL_DYSFUNCTION(30062, "presAutoInquiryToRealForLiverRenalDysfunction", "肝、肾功能异常是否流向真人问诊", "医生开方配置-开方基础属性配置-自动开方配置-肝、肾功能异常是否流向真人问诊",
        GLOBAL),
    PRES_AUTO_INQUIRY_TO_REAL_FOR_PREGNANCY_LACTATION(30063, "presAutoInquiryToRealForPregnancyLactation", "妊娠、哺乳期是否流向真人问诊", "医生开方配置-开方基础属性配置-自动开方配置-妊娠、哺乳期是否流向真人问诊",
        GLOBAL),
    PRES_AUTO_INQUIRY_TO_REAL_FOR_MULTI_DIAGNOSE(30064, "presAutoInquiryToRealForMultiDiagnose", "多诊断是否流向真人问诊", "医生开方配置-开方基础属性配置-自动开方配置-多诊断是否流向真人问诊", GLOBAL),
    PRES_AUTO_INQUIRY_TO_REAL_FOR_SPECIAL_AGE_RANGE(30065, "presAutoInquiryToRealForSpecialAgeRange", "特定年龄区间是否流向真人问诊", "医生开方配置-开方基础属性配置-自动开方配置-特定年龄区间是否流向真人问诊",
        GLOBAL),
    PRES_AUTO_INQUIRY_RETURN_TO_REAL(30066, "presAutoInquiryReturnToReal", "自动开方是否回流真人", "医生开方配置-开方基础属性配置-自动开方配置-自动开方是否回流真人", GLOBAL),


    PRES_MEDICARE_CODE_DISPLAY(30007, "presMedicareCodeDisplay", "医保代码展示", "医生开方配置-医保代码展示", STORE, AREA),
    PRES_ABANDON_PRESCRIPTION(30008, "presAbandonPrescription", "废弃处方", "医生开方配置-废弃处方", STORE, AREA),
    PRES_ALL_REAL_PEOPLE_INQUIRY(30009, "presAllRealPeopleInquiry", "全真人问诊", "医生开方配置-全真人问诊", STORE, AREA),


    // 药师审方配置
    PRES_PHARMACIST_REVIEW(40001, "presPharmacistReview", "医院药师审核", "药师审方配置-医院药师审核", STORE, AREA),
    /**
     * {@link PharmacistAuditAreaTypeEnum}
     */
    PRES_PHARMACIST_AREA_TYPE(40002, "presPharmacistAreaType", "地区审方管理", "药师审方配置-地区审方管理", AREA),

    ;

    private final int type;

    private final String field;

    private final String name;

    private final String description;

    /**
     * 优先级顺序，从高到低 (同时限定适用目标类型)
     */
    private final InquiryTargetTypeEnum[] targetTypePriorities;

    InquiryOptionTypeEnum(int type, String field, String name, String description,
        InquiryTargetTypeEnum... targetTypePriorities) {
        this.type = type;
        this.field = field;
        this.name = name;
        this.description = description;
        this.targetTypePriorities = targetTypePriorities;
    }

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(InquiryOptionTypeEnum::getType).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }


    public static InquiryOptionTypeEnum fromType(int type) {
        return Arrays.stream(values())
            .filter(value -> Objects.equals(value.getType(), type))
            .findFirst()
            .orElse(null);
    }

    /**
     * 获取问诊前端流程控制选项type
     *
     * @return
     */
    public static InquiryOptionTypeEnum[] getInquiryFrontFormProcessOptions() {
        List<Integer> list = Stream.of(10004, 10005, 10006, 10007, 10008, 20001, 20002, 20003, 20006, 20007, 20009, 20010, 20011, 20012, 20013, 20014, 20015, 20018, 30008).toList();
        return Arrays.stream(values()).filter(value -> list.contains(value.type)).toArray(InquiryOptionTypeEnum[]::new);
    }


    public static InquiryOptionTypeEnum[] getInquiryDoctorProcessOptions() {
        List<Integer> list = Stream.of(10004, 10005, 10006, 10007, 20002, 20003, 20009, 20010, 20012, 20014, 30002, 30003).toList();
        return Arrays.stream(values()).filter(value -> list.contains(value.type)).toArray(InquiryOptionTypeEnum[]::new);
    }

}